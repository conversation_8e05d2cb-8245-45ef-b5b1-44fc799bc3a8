import React from 'react';
import {
  AppstoreOutlined,
  RobotOutlined,
  GiftOutlined,
  ApiOutlined,
  ShopOutlined
} from '@ant-design/icons';

// 统一插件市场页面
import PluginMarket from '../pages/project/plugins/plugin-market';
import PluginDetail from '../pages/project/plugins/plugin-market/PluginDetail';

// 插件组件
import DingTalkRobot from '../pages/project/plugins/dingtalk/DingTalkRobot';
import MarketingGameList from '../pages/project/plugins/marketing_game';
import PrizeManagement from '../pages/project/plugins/marketing_game/prizes';
import ContentModerationPlugin from '../pages/project/plugins/content_moderation/ContentModerationPlugin';
import SAPIPlugin from '../pages/project/plugins/sapi';
import AICustomerServicePlugin from '../pages/project/plugins/ai_customer_service';

// 插件路由配置
const pluginRoutes = [
  // 统一插件市场
  {
    path: '/project/plugins/plugin-market',
    name: '插件市场',
    icon: <ShopOutlined />,
    component: PluginMarket,
    exact: true,
    permissions: ['project_admin']
  },
  {
    path: '/project/plugins/plugin-market/detail/:id',
    name: '插件详情',
    component: PluginDetail,
    exact: true,
    permissions: ['project_admin'],
    hideInMenu: true
  },

  // 市场插件
  {
    path: '/project/plugins/dingtalk',
    name: '钉钉机器人',
    icon: <RobotOutlined />,
    component: DingTalkRobot,
    exact: true,
    permissions: ['project_admin', 'project_user'],
    type: 'marketplace'
  },
  {
    path: '/project/plugins/marketing_game',
    name: '营销游戏',
    icon: <GiftOutlined />,
    component: MarketingGameList,
    exact: true,
    permissions: ['project_admin', 'project_user'],
    type: 'marketplace'
  },
  {
    path: '/project/plugins/marketing_game/prizes/:gameId',
    name: '奖品管理',
    component: PrizeManagement,
    exact: true,
    permissions: ['project_admin', 'project_user'],
    hideInMenu: true,
    type: 'marketplace'
  },

  // AI 插件
  {
    path: '/project/ai/plugin/content_moderation',
    name: '内容审核',
    icon: <ApiOutlined />,
    component: ContentModerationPlugin,
    exact: true,
    permissions: ['project_admin', 'project_user'],
    type: 'ai'
  },

  // 智能助手集成平台
  {
    path: '/project/plugins/sapi',
    name: '智能助手集成平台',
    icon: <ApiOutlined />,
    component: SAPIPlugin,
    exact: true,
    permissions: ['project_admin', 'project_user'],
    type: 'marketplace'
  },

  // AI 智能体 - 超级全能客服
  {
    path: '/project/plugins/ai_customer_service',
    name: 'AI 智能体 - 超级全能客服',
    icon: <RobotOutlined />,
    component: AICustomerServicePlugin,
    exact: true,
    permissions: ['project_admin', 'project_user'],
    type: 'marketplace'
  }
];

export default pluginRoutes;
