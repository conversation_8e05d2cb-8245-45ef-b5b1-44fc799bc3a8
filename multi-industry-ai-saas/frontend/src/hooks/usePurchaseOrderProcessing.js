import { useState, useCallback, useRef, useEffect } from 'react';
import { message, notification, But<PERSON>, Modal } from 'antd';
import apiService from '../services/api';
import AsyncTaskService from '../services/AsyncTaskService';

/**
 * 采购分拨单处理Hook
 * 提供混合模式处理：智能检测文件类型，选择最佳处理方式
 */
export const usePurchaseOrderProcessing = () => {
  const [processing, setProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState('idle'); // idle, sync_trying, fallback_to_async, async_processing, completed, error
  const [processingMessage, setProcessingMessage] = useState('');
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  const [taskInfo, setTaskInfo] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState('idle');
  
  const abortControllerRef = useRef(null);

  // 页面刷新保护
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (processing) {
        const message = '正在进行AI识别处理，确定要离开吗？这将中断当前操作。';
        event.preventDefault();
        event.returnValue = message;
        return message;
      }
    };

    if (processing) {
      window.addEventListener('beforeunload', handleBeforeUnload);

      return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
      };
    }
  }, [processing]);

  /**
   * 检查文件是否为图片类型
   * @param {string} fileName 文件名
   * @returns {boolean} 是否为图片
   */
  const isImageFile = useCallback((fileName) => {
    if (!fileName) return false;
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return imageExtensions.includes(ext);
  }, []);

  /**
   * 检查文件是否为Excel类型
   * @param {string} fileName 文件名
   * @returns {boolean} 是否为Excel文件
   */
  const isExcelFile = useCallback((fileName) => {
    if (!fileName) return false;
    const excelExtensions = ['.xlsx', '.xls', '.csv'];
    const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return excelExtensions.includes(ext);
  }, []);

  // 显示识别结果 - 兼容同步和异步两种格式
  const showAIResult = useCallback((resultData) => {
    console.log('showAIResult 接收到的数据:', resultData);
    
    // 智能解析预览数据 - 兼容多种格式
    let preview = null;
    let purchaseItems = [];
    let distributionDestinations = [];
    let processingInfo = null;
    
    if (resultData) {
      // 优先从统一格式中获取
      if (resultData.purchase_items) {
        purchaseItems = resultData.purchase_items;
        distributionDestinations = resultData.distribution_destinations || [];
        preview = resultData.preview || {};
        processingInfo = resultData.processing_info;
      }
      // 其次从预览格式中获取
      else if (resultData.preview) {
        preview = resultData.preview;
        purchaseItems = preview.purchase_items || [];
        distributionDestinations = preview.distribution_destinations || [];
        processingInfo = resultData.processing_info;
      }
      // 最后从data中获取
      else if (resultData.data && resultData.data.preview) {
        preview = resultData.data.preview;
        purchaseItems = preview.purchase_items || [];
        distributionDestinations = preview.distribution_destinations || [];
        processingInfo = resultData.data.processing_info;
      }
      // 兼容旧格式
      else if (resultData.items) {
        purchaseItems = resultData.items;
        distributionDestinations = resultData.distribution_destinations || [];
      }
    }
    
    if (purchaseItems.length === 0 && distributionDestinations.length === 0) {
      console.error('无法找到有效的预览数据:', resultData);
      message.error('未找到有效的识别数据，无法显示预览');
      return;
    }

    console.log('解析到的商品数据:', purchaseItems);

    // 根据处理方法确定标题和描述
    const method = processingInfo?.method || resultData.method || 'unknown';
    const methodDisplayName = method === 'template' ? '模板匹配' : 
                            method === 'ai' ? 'AI智能识别' : 
                            method === 'hybrid' ? '混合模式' : '智能处理';
    
    Modal.info({
      title: `${methodDisplayName}结果`,
      width: 900,
      content: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: 8 }}>
              🎉 {methodDisplayName}成功！
            </div>
            <div style={{ color: '#666' }}>
              已识别您上传的采购分拨单，共识别到 <strong>{purchaseItems.length}</strong> 个商品
              {distributionDestinations.length > 0 && (
                <span>，分拨到 <strong>{distributionDestinations.length}</strong> 个门店</span>
              )}
              {purchaseItems.some(item => item.product_specification) && (
                <span style={{ color: '#1890ff' }}>
                  ，包含多规格商品
                </span>
              )}
            </div>
            {processingInfo && (
              <div style={{ marginTop: 8, padding: 8, background: '#f0f9ff', borderRadius: 4 }}>
                <span style={{ fontSize: '12px', color: '#1890ff' }}>
                  处理信息：{method === 'template' ? '使用Excel模板匹配' : '使用AI视觉识别'}
                  {processingInfo.confidence && ` | 置信度: ${(processingInfo.confidence * 100).toFixed(1)}%`}
                  {processingInfo.total_rows && ` | 总行数: ${processingInfo.total_rows}`}
                </span>
              </div>
            )}
          </div>
          
          {purchaseItems.length > 0 && (
            <div style={{ marginBottom: 16 }}>
              <div style={{ fontWeight: 'bold', marginBottom: 12 }}>识别的商品列表：</div>
              <div style={{ 
                maxHeight: 400, 
                overflow: 'auto',
                border: '1px solid #d9d9d9',
                borderRadius: 6
              }}>
                {purchaseItems.map((item, index) => (
                  <div 
                    key={index} 
                    style={{ 
                      padding: 12, 
                      borderBottom: index < purchaseItems.length - 1 ? '1px solid #f0f0f0' : 'none',
                      background: index % 2 === 0 ? '#fafafa' : '#fff'
                    }}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div>
                        <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                          {item.product_name}
                        </div>
                        <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                          规格: {item.product_specification || '标准规格'} | 
                          单位: {item.product_unit || '个'} | 
                          编码: {item.product_code || '系统自动生成'}
                        </div>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <div style={{ fontSize: '14px' }}>
                          数量: <strong>{item.quantity}</strong>
                        </div>
                        <div style={{ fontSize: '14px' }}>
                          单价: <strong>¥{item.unit_price}</strong>
                        </div>
                        <div style={{ fontSize: '14px', color: '#f56a00' }}>
                          金额: <strong>¥{item.total_amount}</strong>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div style={{ marginTop: 12, textAlign: 'right', fontSize: '16px' }}>
                <strong>
                  总金额: ¥{purchaseItems.reduce((sum, item) => sum + (item.total_amount || 0), 0).toFixed(2)}
                </strong>
              </div>
            </div>
          )}
          
          <div style={{ 
            background: '#e6f7ff', 
            border: '1px solid #91d5ff',
            borderRadius: 6,
            padding: 12,
            marginTop: 16
          }}>
            <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: 8 }}>
              📋 下一步操作：
            </div>
            <div style={{ fontSize: '13px', lineHeight: '1.5' }}>
              1. 请仔细核对上述识别结果的准确性<br/>
              2. 如需修改，可前往"采购管理"页面编辑<br/>
              3. 确认无误后，可直接生成采购订单
            </div>
          </div>
        </div>
      ),
      onOk() {
        message.success('请前往采购管理页面进行后续操作');
      },
    });
  }, []);

  /**
   * 处理AI预览
   * @param {string} fileId 文件ID
   * @param {string} uploadType 上传类型 
   * @param {string} warehouseId 仓库ID
   * @param {string} distributionMode 分拨模式
   * @param {string} distributionItems 分拨商品
   * @param {string} processingMode 处理模式
   * @param {number} syncTimeout 同步超时时间
   */
  const processAIPreview = useCallback(async (
    fileId,
    uploadType = 'both',
    warehouseId = undefined,
    distributionMode = 'direct',
    distributionItems = '[]',
    processingMode = 'auto',
    syncTimeout = 25000
  ) => {
    if (!fileId) {
      message.error('文件ID不能为空');
      return;
    }

    console.log('processAIPreview调用参数:', {
      fileId,
      uploadType,
      warehouseId,
      distributionMode,
      distributionItems,
      processingMode,
      syncTimeout
    });

    setProcessing(true);
    setProgress(0);
    setStatus('idle');
    setProcessingMessage('');
    setResult(null);
    setError(null);
    setTaskInfo(null);

    try {
      // 首先获取文件信息，判断文件类型
      let fileInfo = null;
      try {
        fileInfo = await apiService.project.space.getFileInfo(fileId);
        console.log('文件信息:', fileInfo);
      } catch (error) {
        console.warn('无法获取文件信息，使用默认混合模式处理:', error);
      }

      // 根据文件类型智能选择处理方式
      const fileName = fileInfo?.data?.name || '';
      const isImage = isImageFile(fileName);
      const isExcel = isExcelFile(fileName);

      console.log('文件类型检测结果:', { fileName, isImage, isExcel });

      // 智能处理模式选择
      let finalProcessingMode = processingMode;
      let shouldSkipSync = false;

      if (isImage) {
        // 图片文件：直接使用AI处理，跳过同步模板处理
        finalProcessingMode = 'ai_only';
        shouldSkipSync = true;
        console.log('检测到图片文件，直接使用AI智能识别模式');
        message.info('检测到图片文件，将使用AI智能识别处理', 2);
      } else if (isExcel && processingMode === 'auto') {
        // Excel文件：优先使用模板处理
        finalProcessingMode = 'template_priority';
        console.log('检测到Excel文件，优先使用模板匹配模式');
      } else if (!isImage && !isExcel) {
        // 未知格式：使用AI处理
        finalProcessingMode = 'ai_only';
        shouldSkipSync = true;
        console.log('检测到未知格式文件，使用AI智能识别模式');
        message.info('文件格式未知，将使用AI智能识别处理', 2);
      }

      console.log('开始调用 AsyncTaskService.executeHybridTask, shouldSkipSync:', shouldSkipSync);

      await AsyncTaskService.executeHybridTask({
        // 同步处理函数 - 根据文件类型智能跳过
        syncAction: shouldSkipSync ? null : async () => {
          abortControllerRef.current = new AbortController();
          
          const params = {
            file_id: fileId,
            upload_type: uploadType,
            warehouse_id: warehouseId,
            distribution_mode: distributionMode,
            distribution_items: distributionItems || '[]'
          };

          console.log('同步处理API调用参数:', params);
          
          try {
            const response = await apiService.project.purchaseOrder.previewUpload(params, {
              signal: abortControllerRef.current.signal,
              timeout: syncTimeout
            });
            
            if (response && response.success) {
              return response;  // 返回完整的response，而不是只返回data
            } else {
              throw new Error(response?.message || '同步处理失败');
            }
          } catch (error) {
            // 如果是422错误（图片文件需要AI处理），抛出特殊错误让系统转异步
            if (error.response?.status === 422) {
              const errorDetail = error.response?.data?.detail;
              
              // 检查是否为新的图片文件检测响应格式
              if (typeof errorDetail === 'object' && errorDetail.code === 'IMAGE_FILE_DETECTED') {
                console.log('检测到图片文件，建议使用AI处理:', errorDetail);
                throw new Error('IMAGE_FILE_NEEDS_AI');
              } else if (typeof errorDetail === 'string' && errorDetail.includes('图片文件')) {
                throw new Error('IMAGE_FILE_NEEDS_AI');
              } else {
                throw new Error('IMAGE_FILE_NEEDS_AI');
              }
            }
            throw error;
          }
        },

        // 异步处理函数
        asyncAction: async () => {
          const params = {
            file_id: fileId,
            upload_type: uploadType,
            warehouse_id: warehouseId,
            distribution_mode: distributionMode,
            distribution_items: distributionItems || '[]',
            use_ai: true,
            processing_mode: finalProcessingMode
          };

          console.log('异步处理API调用参数:', params);
          
          const response = await apiService.project.purchaseOrder.previewUploadWithAi(params);
          console.log('异步处理API响应:', response);
          
          if (response && response.success) {
            console.log('异步任务创建成功，任务ID:', response.data?.task_id);
            
            // 显示任务创建成功的通知
            notification.success({
              message: 'AI处理任务已启动',
              description: `任务ID: ${response.data?.task_id || '未知'}，正在智能识别采购分拨单内容，请稍候...`,
              duration: 5,
              placement: 'topRight'
            });
            
            return response;
          } else {
            throw new Error(response?.message || '异步任务创建失败');
          }
        },

        syncTimeout,

        // 进度更新回调
        onProgress: (progress) => {
          console.log('AI处理进度更新:', progress);
          setUploadProgress(progress.progress || 0);
          
          if (progress.message) {
            setUploadStatus(progress.message);
          }
        },

        // 任务完成回调
        onComplete: (result) => {
          console.log('处理完成，原始结果:', result);
          setUploadProgress(100);
          setUploadStatus('识别完成');
          
          // 检查是否成功
          // 兼容同步和异步两种不同的返回格式
          const isSuccess = result.success === true || 
                           result.success === 'true' || 
                           (result.message && result.message.includes('completed')) ||
                           (result.result && typeof result.result === 'object');
          
          if (!isSuccess) {
            const errorMessage = result.message || result.error_message || '识别失败';
            console.error('识别失败：', errorMessage, result);
            setStatus('error');
            setError(new Error(errorMessage));
            setProcessing(false);
            
            notification.error({
              message: '识别失败',
              description: errorMessage,
              duration: 5
            });
            return;
          }
          
          // 成功情况 - 智能处理同步和异步两种数据格式
          console.log('识别成功，原始结果数据:', result);
          
          let actualResult = result;
          let processingMethod = 'unknown';
          
          // 检测数据来源：同步(模板匹配)还是异步(AI识别)
          if (result.data && result.data.processing_info) {
            // 同步处理结果格式: {success: true, data: {preview: {...}, processing_info: {...}}}
            processingMethod = result.data.processing_info.method || 'template';
            console.log('检测到同步处理结果，方法:', processingMethod);
            
            // 转换为统一的结果格式
            const preview = result.data.preview || {};
            actualResult = {
              success: true,
              method: processingMethod,
              preview: preview,
              purchase_items: preview.purchase_items || [],
              distribution_destinations: preview.distribution_destinations || [],
              processing_info: result.data.processing_info,
              validation_results: result.data.validation_results || {},
              file_id: result.data.file_id
            };
          } else if (result.result && typeof result.result === 'object') {
            // 异步处理结果格式 - 处理可能的双重嵌套结构
            processingMethod = 'ai';
            console.log('检测到异步处理结果');
            
            actualResult = {
              ...result,
              ...result.result
            };
            
            // 如果内层还有result，继续展开
            if (result.result.result && typeof result.result.result === 'object') {
              actualResult = {
                ...result,
                ...result.result.result,
                success: true
              };
            }
            
            // 确保数据结构完整
            if (!actualResult.purchase_items) {
              actualResult.purchase_items = actualResult.items || [];
            }
            if (!actualResult.distribution_destinations) {
              actualResult.distribution_destinations = [];
            }
          } else {
            // 直接的结果格式
            processingMethod = result.method || 'unknown';
            actualResult = result;
          }
          
          console.log('处理后的统一结果数据:', actualResult);
          
          // 数据统计信息 - 兼容不同的字段名
          const purchaseItems = actualResult.purchase_items || actualResult.items || [];
          const distributionDestinations = actualResult.distribution_destinations || [];
          
          const itemCount = purchaseItems.length;
          const storeCount = distributionDestinations.length;
          const hasValidDistribution = distributionDestinations.some(dest => 
            dest.items?.length > 0 && dest.items.some(item => item.quantity > 0)
          );
          
          console.log(`数据统计: ${itemCount}个商品, ${storeCount}个门店, 有效分拨: ${hasValidDistribution}`);
          
          // 根据处理方法显示不同的成功消息
          const methodDisplayName = processingMethod === 'template' ? '模板匹配' : 
                                  processingMethod === 'ai' ? 'AI智能识别' : 
                                  processingMethod === 'hybrid' ? '混合模式' : '智能处理';
          
          // 显示成功通知
          const successNotificationKey = notification.success({
            message: `${methodDisplayName}成功！`,
            description: `已识别 ${itemCount} 个商品${storeCount > 1 ? `，分拨到 ${storeCount} 个门店` : ''}`,
            duration: 8,
            placement: 'topRight',
            actions: (
              <Button 
                type="primary" 
                size="small"
                onClick={() => {
                  showAIResult(actualResult);
                  // 关闭这个特定的通知
                  notification.destroy(successNotificationKey);
                }}
              >
                查看识别结果
              </Button>
            ),
          });
          
          // 更新处理状态
          setStatus('completed');
          setResult(actualResult);
          setProcessing(false);
          
          // 只有异步处理才提示任务管理
          if (processingMethod === 'ai') {
            setTimeout(() => {
              notification.info({
                message: '提示',
                description: '您也可以在"任务管理 > 异步任务监控"中查看详细的处理记录',
                duration: 6,
                placement: 'bottomRight'
              });
            }, 2000);
          }
        },

        // 错误处理回调
        onError: (error) => {
          console.error('AI处理失败:', error);
          setUploadProgress(0);
          setUploadStatus('处理失败');
          setStatus('error');
          setError(error);
          setProcessing(false);
          
          // 解析错误信息
          let errorMessage = '处理过程中出现错误，请重试';
          
          if (error && error.message) {
            errorMessage = error.message;
          } else if (error && error.response && error.response.data) {
            const errorData = error.response.data;
            if (errorData.detail) {
              errorMessage = errorData.detail;
            } else if (errorData.message) {
              errorMessage = errorData.message;
            }
          } else if (typeof error === 'string') {
            errorMessage = error;
          }
          
          notification.error({
            message: 'AI处理失败',
            description: errorMessage,
            duration: 5,
            placement: 'topRight'
          });
        },

        // 转异步回调
        onFallbackToAsync: () => {
          setStatus('fallback_to_async');
          setProcessingMessage('正在使用AI智能处理...');
          
          if (isImage) {
            message.info('正在使用AI智能识别图片内容，请稍候...', 3);
          } else {
            message.info('处理时间较长，已转为后台AI智能处理，请稍候...', 3);
          }
        }
      });

    } catch (err) {
      console.error('processAIPreview执行异常:', err);
      setStatus('error');
      setError(err);
      setProcessingMessage(err.message || '处理失败');
      setProcessing(false);
      
      // 显示错误通知
      notification.error({
        message: '处理失败',
        description: err.message || '启动AI处理时发生错误，请重试',
        duration: 5,
        placement: 'topRight'
      });
    }
  }, [isImageFile, isExcelFile, showAIResult]);

  /**
   * 确认上传处理
   */
  const confirmUpload = useCallback(async (params) => {
    const {
      fileId,
      uploadType,
      warehouseId,
      distributionMode,
      distributionItems
    } = params;

    setProcessing(true);
    setStatus('processing');
    setProcessingMessage('正在创建采购分拨单...');

    try {
      const response = await apiService.project.purchaseOrder.confirmUpload({
        file_id: fileId,
        upload_type: uploadType,
        warehouse_id: warehouseId,
        distribution_mode: distributionMode,
        distribution_items: distributionItems || '[]'
      });

      if (response && response.success) {
        setStatus('completed');
        setProcessingMessage('采购分拨单创建成功');
        setResult(response.data);
        message.success('采购分拨单创建成功');
      } else {
        throw new Error(response?.message || '创建失败');
      }
    } catch (err) {
      setStatus('error');
      setError(err);
      setProcessingMessage(err.message || '创建失败');
      message.error(err.message || '创建失败');
    } finally {
      setProcessing(false);
    }
  }, []);

  /**
   * 取消处理
   */
  const cancel = useCallback(() => {
    // 取消HTTP请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // 取消异步任务
    if (taskInfo && taskInfo.id) {
      AsyncTaskService.cancelTask(taskInfo.id).catch(console.error);
    }

    setProcessing(false);
    setStatus('cancelled');
    setProcessingMessage('处理已取消');
  }, [taskInfo]);

  /**
   * 重置状态
   */
  const reset = useCallback(() => {
    setProcessing(false);
    setProgress(0);
    setStatus('idle');
    setProcessingMessage('');
    setResult(null);
    setError(null);
    setTaskInfo(null);
  }, []);

  // 中止处理
  const abortProcessing = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setProcessing(false);
    setStatus('cancelled');
    setProcessingMessage('已取消');
  }, []);

  return {
    // 状态
    processing,
    progress,
    status,
    processingMessage,
    result,
    error,
    taskInfo,
    uploadProgress,
    uploadStatus,
    
    // 方法
    processAIPreview,
    confirmUpload,
    cancel,
    reset,
    abortProcessing,
    showAIResult,
    
    // 状态判断
    isIdle: status === 'idle',
    isSyncTrying: status === 'sync_trying',
    isFallbackToAsync: status === 'fallback_to_async',
    isAsyncProcessing: status === 'async_processing',
    isCompleted: status === 'completed',
    isError: status === 'error',
    isCancelled: status === 'cancelled'
  };
}; 