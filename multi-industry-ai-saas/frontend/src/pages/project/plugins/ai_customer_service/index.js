import React, { useState, useEffect } from 'react';
import { Card, Tabs, Typography, <PERSON>ert, Button, Space, Badge, Statistic, Row, Col } from 'antd';
import {
  CustomerServiceOutlined,
  RobotOutlined,
  MessageOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  <PERSON>boltOutlined,
  GlobalOutlined,
  BranchesOutlined,
  BulbOutlined,
  ShareAltOutlined,
  WechatOutlined,
  DingdingOutlined,
  ApiOutlined
} from '@ant-design/icons';
import { useAuth } from '../../../../contexts/AuthContext';

// 导入组件
import SessionManagement from './components/SessionManagement';
import PlatformManagement from './components/PlatformManagement';
import AnalyticsDashboard from './components/AnalyticsDashboard';
import TicketManagement from './components/TicketManagement';
import MarketingManagement from './components/MarketingManagement';
import LearningOptimization from './components/LearningOptimization';
import PublicWidget from './components/PublicWidget';
import BusinessFlow from './components/BusinessFlow';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

const AICustomerServicePlugin = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('1');
  const [stats, setStats] = useState({
    totalSessions: 0,
    activeSessions: 0,
    totalMessages: 0,
    avgSatisfaction: 0,
    platformCount: 0,
    ticketCount: 0
  });

  // 判断是否为管理员
  const isAdmin = user && (
    user.role === 'admin' ||
    user.is_system_admin ||
    user.is_super_admin ||
    user.is_tenant_admin ||
    user.is_service_provider_admin ||
    user.is_project_admin
  );

  // 获取统计数据
  const fetchStats = async () => {
    try {
      // 这里调用API获取统计数据
      // 暂时使用模拟数据
      setStats({
        totalSessions: 1256,
        activeSessions: 23,
        totalMessages: 8945,
        avgSatisfaction: 4.2,
        platformCount: 4,
        ticketCount: 15
      });
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return (
    <Card>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <RobotOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          AI 智能体 - 超级全能客服
        </Title>
        <Paragraph>
          基于 LangBot 架构设计的多平台智能客服系统，整合 AI 助理、知识库 RAG、通知系统、任务系统，
          打造超级客服 AI 智能体。支持微信、企业微信、钉钉、飞书等多平台，具备智能学习优化和自我完善能力。
        </Paragraph>

        {/* 统计概览 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="总会话数"
                value={stats.totalSessions}
                prefix={<MessageOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="活跃会话"
                value={stats.activeSessions}
                prefix={<Badge status="processing" />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="客户满意度"
                value={stats.avgSatisfaction}
                precision={1}
                suffix="/ 5.0"
                prefix={<ThunderboltOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="接入平台"
                value={stats.platformCount}
                prefix={<GlobalOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        <Alert
          message="功能特色"
          description={
            <div>
              <Row gutter={[16, 8]}>
                <Col xs={24} sm={12} md={8}>
                  <Space>
                    <WechatOutlined style={{ color: '#52c41a' }} />
                    <span>多平台智能客服（微信公众号、企业微信、钉钉等）</span>
                  </Space>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Space>
                    <BranchesOutlined style={{ color: '#1890ff' }} />
                    <span>智能业务流转（投诉转运营、招商转商务）</span>
                  </Space>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Space>
                    <BulbOutlined style={{ color: '#faad14' }} />
                    <span>智能学习优化（自动改进回复质量）</span>
                  </Space>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Space>
                    <ShareAltOutlined style={{ color: '#722ed1' }} />
                    <span>公共分享版本（嵌入任何网站）</span>
                  </Space>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Space>
                    <BarChartOutlined style={{ color: '#eb2f96' }} />
                    <span>深度数据分析（客户画像、行为分析）</span>
                  </Space>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <Space>
                    <ThunderboltOutlined style={{ color: '#f5222d' }} />
                    <span>智能营销系统（自动推送券、个性化推荐）</span>
                  </Space>
                </Col>
              </Row>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={<span><CustomerServiceOutlined /> 概览</span>}
          key="1"
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: 24 }}>
            <Space direction="vertical" size="large">
              <div>
                <Title level={4}>🤖 多平台智能客服</Title>
                <Paragraph>
                  支持个人微信、微信公众号、企业微信、钉钉、飞书等多平台接入，
                  提供统一的智能客服体验。微信公众号支持15秒响应机制和异步消息处理。
                </Paragraph>
                <Button type="primary" onClick={() => setActiveTab('3')}>
                  <GlobalOutlined /> 平台管理
                </Button>
              </div>

              <div>
                <Title level={4}>🎯 智能业务流转</Title>
                <Paragraph>
                  基于意图识别，自动将客户问题转入相应业务流程：投诉转运营、招商转商务、
                  技术问题转技术支持，并自动通知相关人员和创建任务。
                </Paragraph>
                <Button type="primary" onClick={() => setActiveTab('8')}>
                  <BranchesOutlined /> 业务流转
                </Button>
              </div>

              <div>
                <Title level={4}>📊 深度数据分析</Title>
                <Paragraph>
                  提供客户行为分析、满意度统计、对话质量评估等深度分析功能，
                  生成详细的分析报告和客户画像。
                </Paragraph>
                <Button type="primary" onClick={() => setActiveTab('4')}>
                  <BarChartOutlined /> 数据分析
                </Button>
              </div>

              <div>
                <Title level={4}>🧠 智能学习优化</Title>
                <Paragraph>
                  基于对话数据自动学习和优化AI回复质量、意图识别准确性，
                  实现系统的自我完善和持续改进。
                </Paragraph>
                <Button type="primary" onClick={() => setActiveTab('6')}>
                  <BulbOutlined /> 学习优化
                </Button>
              </div>

              <div>
                <Title level={4}>🌐 公共分享版本</Title>
                <Paragraph>
                  提供可嵌入任何网站的客服组件，支持多种分辨率和自定义主题，
                  生成公共访问链接，无需登录即可使用。
                </Paragraph>
                <Button type="primary" onClick={() => setActiveTab('7')}>
                  <ShareAltOutlined /> 公共组件
                </Button>
              </div>

              {isAdmin && (
                <div>
                  <Title level={4}>⚙️ 高级管理功能</Title>
                  <Paragraph>
                    管理员可以配置平台参数、查看详细分析、管理工单和营销活动，
                    以及配置业务流转规则和学习优化参数。
                  </Paragraph>
                  <Space>
                    <Button onClick={() => setActiveTab('2')}>
                      <MessageOutlined /> 会话管理
                    </Button>
                    <Button onClick={() => setActiveTab('5')}>
                      <CustomerServiceOutlined /> 工单管理
                    </Button>
                  </Space>
                </div>
              )}
            </Space>
          </div>
        </TabPane>

        {isAdmin && (
          <TabPane
            tab={<span><MessageOutlined /> 会话管理</span>}
            key="2"
          >
            <SessionManagement />
          </TabPane>
        )}

        <TabPane
          tab={<span><GlobalOutlined /> 平台管理</span>}
          key="3"
        >
          <PlatformManagement />
        </TabPane>

        <TabPane
          tab={<span><BarChartOutlined /> 数据分析</span>}
          key="4"
        >
          <AnalyticsDashboard />
        </TabPane>

        {isAdmin && (
          <TabPane
            tab={<span><CustomerServiceOutlined /> 工单管理</span>}
            key="5"
          >
            <TicketManagement />
          </TabPane>
        )}

        <TabPane
          tab={<span><BulbOutlined /> 学习优化</span>}
          key="6"
        >
          <LearningOptimization />
        </TabPane>

        <TabPane
          tab={<span><ShareAltOutlined /> 公共组件</span>}
          key="7"
        >
          <PublicWidget />
        </TabPane>

        <TabPane
          tab={<span><BranchesOutlined /> 业务流转</span>}
          key="8"
        >
          <BusinessFlow />
        </TabPane>

        {isAdmin && (
          <TabPane
            tab={<span><ThunderboltOutlined /> 智能营销</span>}
            key="9"
          >
            <MarketingManagement />
          </TabPane>
        )}
      </Tabs>
    </Card>
  );
};

export default AICustomerServicePlugin;
