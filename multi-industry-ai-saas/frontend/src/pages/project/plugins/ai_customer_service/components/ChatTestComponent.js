import React, { useState, useRef, useEffect } from 'react';
import {
  Card,
  Input,
  Button,
  Space,
  Avatar,
  Typography,
  Divider,
  Alert,
  Spin,
  message,
  Tag,
  Tooltip
} from 'antd';
import {
  SendOutlined,
  RobotOutlined,
  UserOutlined,
  ReloadOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';

const { TextArea } = Input;
const { Text, Paragraph } = Typography;

const ChatTestComponent = ({ projectId }) => {
  const [messages, setMessages] = useState([
    {
      id: '1',
      type: 'ai',
      content: '您好！我是AI智能客服，有什么可以帮助您的吗？',
      timestamp: new Date(),
      model: 'GPT-4'
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 初始化会话
  const initSession = async () => {
    try {
      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/sessions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          platform_type: 'test',
          platform_user_id: 'test_user_' + Date.now(),
          title: '对话测试会话'
        })
      });

      if (response.ok) {
        const data = await response.json();
        setSessionId(data.session_id);
      }
    } catch (error) {
      console.error('初始化会话失败:', error);
    }
  };

  useEffect(() => {
    if (projectId) {
      initSession();
    }
  }, [projectId]);

  // 发送消息
  const sendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setLoading(true);

    try {
      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/chat`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: inputValue,
          session_id: sessionId,
          platform_type: 'test',
          platform_user_id: 'test_user'
        })
      });

      if (response.ok) {
        const data = await response.json();
        const aiMessage = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: data.response || '抱歉，我暂时无法回答这个问题。',
          timestamp: new Date(),
          model: data.model_used || 'AI',
          confidence: data.confidence,
          response_time: data.response_time
        };
        setMessages(prev => [...prev, aiMessage]);
      } else {
        message.error('发送消息失败');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败');
    } finally {
      setLoading(false);
    }
  };

  // 清空对话
  const clearChat = () => {
    setMessages([
      {
        id: '1',
        type: 'ai',
        content: '您好！我是AI智能客服，有什么可以帮助您的吗？',
        timestamp: new Date(),
        model: 'GPT-4'
      }
    ]);
    initSession();
  };

  // 快速测试问题
  const quickQuestions = [
    '你好，请介绍一下你的功能',
    '我想了解产品价格',
    '如何联系人工客服？',
    '你们的营业时间是什么？',
    '我遇到了技术问题，怎么办？'
  ];

  const handleQuickQuestion = (question) => {
    setInputValue(question);
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div style={{ height: '600px', display: 'flex', flexDirection: 'column' }}>
      <Alert
        message="对话测试说明"
        description="这是一个测试环境，您可以在这里测试AI客服的对话能力。所有对话数据仅用于测试，不会保存到正式系统中。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* 快速测试问题 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Text strong>快速测试问题：</Text>
        <div style={{ marginTop: 8 }}>
          <Space wrap>
            {quickQuestions.map((question, index) => (
              <Button
                key={index}
                size="small"
                onClick={() => handleQuickQuestion(question)}
              >
                {question}
              </Button>
            ))}
          </Space>
        </div>
      </Card>

      {/* 对话区域 */}
      <Card 
        style={{ 
          flex: 1, 
          display: 'flex', 
          flexDirection: 'column',
          overflow: 'hidden'
        }}
        bodyStyle={{ 
          flex: 1, 
          display: 'flex', 
          flexDirection: 'column',
          padding: '16px 16px 0 16px'
        }}
      >
        <div style={{ 
          flex: 1, 
          overflowY: 'auto', 
          marginBottom: 16,
          padding: '0 8px'
        }}>
          {messages.map((msg) => (
            <div key={msg.id} style={{ marginBottom: 16 }}>
              <div style={{ 
                display: 'flex', 
                justifyContent: msg.type === 'user' ? 'flex-end' : 'flex-start',
                alignItems: 'flex-start'
              }}>
                {msg.type === 'ai' && (
                  <Avatar 
                    icon={<RobotOutlined />} 
                    style={{ backgroundColor: '#1890ff', marginRight: 8 }}
                  />
                )}
                
                <div style={{ 
                  maxWidth: '70%',
                  order: msg.type === 'user' ? -1 : 1
                }}>
                  <div style={{
                    background: msg.type === 'user' ? '#1890ff' : '#f5f5f5',
                    color: msg.type === 'user' ? 'white' : 'black',
                    padding: '8px 12px',
                    borderRadius: '8px',
                    marginBottom: '4px'
                  }}>
                    <Paragraph style={{ 
                      margin: 0, 
                      color: msg.type === 'user' ? 'white' : 'inherit'
                    }}>
                      {msg.content}
                    </Paragraph>
                  </div>
                  
                  <div style={{ 
                    fontSize: '12px', 
                    color: '#999',
                    textAlign: msg.type === 'user' ? 'right' : 'left'
                  }}>
                    <Space size={4}>
                      <ClockCircleOutlined />
                      <span>{formatTime(msg.timestamp)}</span>
                      {msg.model && (
                        <Tag size="small" color="blue">{msg.model}</Tag>
                      )}
                      {msg.confidence && (
                        <Tooltip title="AI置信度">
                          <Tag size="small" color="green">
                            {(msg.confidence * 100).toFixed(0)}%
                          </Tag>
                        </Tooltip>
                      )}
                      {msg.response_time && (
                        <Tooltip title="响应时间">
                          <Tag size="small" color="orange">
                            {msg.response_time}ms
                          </Tag>
                        </Tooltip>
                      )}
                    </Space>
                  </div>
                </div>

                {msg.type === 'user' && (
                  <Avatar 
                    icon={<UserOutlined />} 
                    style={{ backgroundColor: '#52c41a', marginLeft: 8 }}
                  />
                )}
              </div>
            </div>
          ))}
          
          {loading && (
            <div style={{ textAlign: 'center', padding: '16px' }}>
              <Spin size="small" />
              <Text style={{ marginLeft: 8 }}>AI正在思考中...</Text>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        <Divider style={{ margin: '8px 0' }} />

        {/* 输入区域 */}
        <div style={{ padding: '0 8px 16px 8px' }}>
          <Space.Compact style={{ width: '100%' }}>
            <TextArea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="输入您的问题..."
              autoSize={{ minRows: 1, maxRows: 3 }}
              onPressEnter={(e) => {
                if (!e.shiftKey) {
                  e.preventDefault();
                  sendMessage();
                }
              }}
            />
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={sendMessage}
              loading={loading}
              disabled={!inputValue.trim()}
            >
              发送
            </Button>
          </Space.Compact>
          
          <div style={{ marginTop: 8, textAlign: 'right' }}>
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={clearChat}
            >
              清空对话
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ChatTestComponent;
