import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Tag,
  Select,
  DatePicker,
  Space,
  Typography,
  Alert,
  Button,
  Spin
} from 'antd';
import {
  MessageOutlined,
  UserOutlined,
  ClockCircleOutlined,
  SmileOutlined,
  TrophyOutlined,
  RiseOutlined
} from '@ant-design/icons';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Title } = Typography;

const AnalyticsDashboard = () => {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState([]);
  const [platform, setPlatform] = useState('all');
  const [stats, setStats] = useState({
    totalSessions: 0,
    totalMessages: 0,
    avgResponseTime: 0,
    satisfactionScore: 0,
    resolutionRate: 0,
    escalationRate: 0
  });
  const [platformStats, setPlatformStats] = useState([]);
  const [intentStats, setIntentStats] = useState([]);
  const [trendData, setTrendData] = useState([]);

  // 获取项目ID
  const getProjectId = () => {
    const projectId = localStorage.getItem('project_id');
    if (projectId && projectId !== 'null' && projectId !== 'undefined') {
      return projectId;
    }

    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    if (userData && userData.project_id) {
      localStorage.setItem('project_id', userData.project_id);
      return userData.project_id;
    }

    return null;
  };

  // 获取分析数据
  const fetchAnalyticsData = async () => {
    setLoading(true);
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        setLoading(false);
        return;
      }

      // 构建查询参数
      const params = new URLSearchParams();
      if (platform !== 'all') {
        params.append('platform', platform);
      }
      if (dateRange && dateRange.length === 2) {
        params.append('start_date', dateRange[0].format('YYYY-MM-DD'));
        params.append('end_date', dateRange[1].format('YYYY-MM-DD'));
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugins/ai-customer-service/analytics/detailed?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();

        // 更新统计数据
        setStats({
          totalSessions: data.total_sessions || 0,
          totalMessages: data.total_messages || 0,
          avgResponseTime: data.avg_response_time || 0,
          satisfactionScore: data.avg_satisfaction || 0,
          resolutionRate: data.resolution_rate || 0,
          escalationRate: data.escalation_rate || 0
        });

        // 更新平台统计
        setPlatformStats(data.platform_stats || []);

        // 更新意图分析
        setIntentStats(data.intent_stats || []);

        // 更新趋势数据
        setTrendData(data.trend_data || []);

      } else {
        // 如果API调用失败，使用空数据
        setStats({
          totalSessions: 0,
          totalMessages: 0,
          avgResponseTime: 0,
          satisfactionScore: 0,
          resolutionRate: 0,
          escalationRate: 0
        });
        setPlatformStats([]);
        setIntentStats([]);
        setTrendData([]);
      }
    } catch (error) {
      console.error('获取分析数据失败:', error);
      setStats({
        totalSessions: 0,
        totalMessages: 0,
        avgResponseTime: 0,
        satisfactionScore: 0,
        resolutionRate: 0,
        escalationRate: 0
      });
      setPlatformStats([]);
      setIntentStats([]);
      setTrendData([]);
    } finally {
      setLoading(false);
    }
  };

  // 当筛选条件改变时重新获取数据
  useEffect(() => {
    fetchAnalyticsData();
  }, [platform, dateRange]);

  const platformColumns = [
    {
      title: '平台',
      dataIndex: 'platform',
      key: 'platform',
      render: (text, record) => <Tag color={record.color}>{text}</Tag>
    },
    {
      title: '会话数',
      dataIndex: 'sessions',
      key: 'sessions',
      sorter: (a, b) => a.sessions - b.sessions
    },
    {
      title: '消息数',
      dataIndex: 'messages',
      key: 'messages',
      sorter: (a, b) => a.messages - b.messages
    },
    {
      title: '满意度',
      dataIndex: 'satisfaction',
      key: 'satisfaction',
      render: (score) => (
        <span style={{ color: score >= 4 ? '#52c41a' : '#faad14' }}>
          {score.toFixed(1)}
        </span>
      ),
      sorter: (a, b) => a.satisfaction - b.satisfaction
    }
  ];

  const intentColumns = [
    {
      title: '意图类型',
      dataIndex: 'intent',
      key: 'intent'
    },
    {
      title: '数量',
      dataIndex: 'count',
      key: 'count',
      sorter: (a, b) => a.count - b.count
    },
    {
      title: '占比',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (percentage) => (
        <div>
          <Progress percent={percentage} size="small" />
          <span style={{ marginLeft: 8 }}>{percentage}%</span>
        </div>
      ),
      sorter: (a, b) => a.percentage - b.percentage
    }
  ];

  return (
    <div>
      <Card title="数据分析">
        <Alert
          message="数据分析功能"
          description="提供客户行为分析、满意度统计、对话质量评估等深度分析功能，帮助优化客服质量。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        {/* 筛选条件 */}
        <div style={{ marginBottom: 24 }}>
          <Space>
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              onChange={setDateRange}
            />
            <Select
              placeholder="选择平台"
              style={{ width: 150 }}
              value={platform}
              onChange={setPlatform}
            >
              <Option value="all">全部平台</Option>
              <Option value="wechat_mp">微信公众号</Option>
              <Option value="wechat_work">企业微信</Option>
              <Option value="dingtalk">钉钉</Option>
              <Option value="feishu">飞书</Option>
            </Select>
          </Space>
        </div>

        {/* 核心指标 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="总会话数"
                value={stats.totalSessions}
                prefix={<MessageOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="总消息数"
                value={stats.totalMessages}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="平均响应时间"
                value={stats.avgResponseTime}
                precision={1}
                suffix="秒"
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="客户满意度"
                value={stats.satisfactionScore}
                precision={1}
                suffix="/ 5.0"
                prefix={<SmileOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 性能指标 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12}>
            <Card title="解决率" size="small">
              <Progress
                type="circle"
                percent={stats.resolutionRate}
                format={percent => `${percent}%`}
                strokeColor="#52c41a"
              />
              <div style={{ textAlign: 'center', marginTop: 16 }}>
                <span style={{ color: '#52c41a', fontSize: 16, fontWeight: 'bold' }}>
                  {stats.resolutionRate}%
                </span>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={12}>
            <Card title="升级率" size="small">
              <Progress
                type="circle"
                percent={stats.escalationRate}
                format={percent => `${percent}%`}
                strokeColor="#faad14"
              />
              <div style={{ textAlign: 'center', marginTop: 16 }}>
                <span style={{ color: '#faad14', fontSize: 16, fontWeight: 'bold' }}>
                  {stats.escalationRate}%
                </span>
              </div>
            </Card>
          </Col>
        </Row>

        {/* 平台统计 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col xs={24} lg={12}>
            <Card title="平台统计" size="small">
              <Table
                columns={platformColumns}
                dataSource={platformStats}
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="意图分析" size="small">
              <Table
                columns={intentColumns}
                dataSource={intentStats}
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
        </Row>

        {/* 趋势分析 */}
        <Card
          title="趋势分析"
          size="small"
          extra={
            <Button
              type="primary"
              size="small"
              onClick={fetchAnalyticsData}
              loading={loading}
            >
              刷新数据
            </Button>
          }
        >
          {trendData.length > 0 ? (
            <Row gutter={16}>
              <Col span={24}>
                <Table
                  columns={[
                    {
                      title: '日期',
                      dataIndex: 'date',
                      key: 'date'
                    },
                    {
                      title: '会话数',
                      dataIndex: 'sessions',
                      key: 'sessions',
                      render: (value) => <span style={{ color: '#1890ff' }}>{value}</span>
                    },
                    {
                      title: '消息数',
                      dataIndex: 'messages',
                      key: 'messages',
                      render: (value) => <span style={{ color: '#52c41a' }}>{value}</span>
                    },
                    {
                      title: '平均响应时间',
                      dataIndex: 'avg_response_time',
                      key: 'avg_response_time',
                      render: (value) => <span>{value}秒</span>
                    },
                    {
                      title: '满意度',
                      dataIndex: 'satisfaction',
                      key: 'satisfaction',
                      render: (value) => (
                        <span style={{ color: value >= 4 ? '#52c41a' : '#faad14' }}>
                          {value ? value.toFixed(1) : '-'}
                        </span>
                      )
                    }
                  ]}
                  dataSource={trendData}
                  pagination={{ pageSize: 7 }}
                  size="small"
                  loading={loading}
                />
              </Col>
            </Row>
          ) : (
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Spin spinning={loading}>
                <div style={{ color: '#999' }}>
                  {loading ? '正在加载趋势数据...' : '暂无趋势数据'}
                </div>
              </Spin>
            </div>
          )}
        </Card>
      </Card>
    </div>
  );
};

export default AnalyticsDashboard;
