import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Tag,
  Select,
  DatePicker,
  Space,
  Typography,
  Alert
} from 'antd';
import {
  MessageOutlined,
  UserOutlined,
  ClockCircleOutlined,
  SmileOutlined,
  TrophyOutlined,
  RiseOutlined
} from '@ant-design/icons';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Title } = Typography;

const AnalyticsDashboard = () => {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState([]);
  const [platform, setPlatform] = useState('all');
  const [stats, setStats] = useState({
    totalSessions: 1256,
    totalMessages: 8945,
    avgResponseTime: 2.3,
    satisfactionScore: 4.2,
    resolutionRate: 85.6,
    escalationRate: 12.4
  });

  // 平台统计数据
  const platformStats = [
    {
      key: '1',
      platform: '微信公众号',
      sessions: 456,
      messages: 3245,
      satisfaction: 4.3,
      color: 'green'
    },
    {
      key: '2',
      platform: '企业微信',
      sessions: 324,
      messages: 2156,
      satisfaction: 4.1,
      color: 'blue'
    },
    {
      key: '3',
      platform: '钉钉',
      sessions: 298,
      messages: 1987,
      satisfaction: 4.0,
      color: 'orange'
    },
    {
      key: '4',
      platform: '飞书',
      sessions: 178,
      messages: 1557,
      satisfaction: 4.4,
      color: 'purple'
    }
  ];

  // 意图分析数据
  const intentStats = [
    { intent: '产品咨询', count: 345, percentage: 28.8 },
    { intent: '技术支持', count: 256, percentage: 20.5 },
    { intent: '价格询问', count: 198, percentage: 16.2 },
    { intent: '投诉建议', count: 156, percentage: 12.4 },
    { intent: '其他', count: 267, percentage: 22.1 }
  ];

  const platformColumns = [
    {
      title: '平台',
      dataIndex: 'platform',
      key: 'platform',
      render: (text, record) => <Tag color={record.color}>{text}</Tag>
    },
    {
      title: '会话数',
      dataIndex: 'sessions',
      key: 'sessions',
      sorter: (a, b) => a.sessions - b.sessions
    },
    {
      title: '消息数',
      dataIndex: 'messages',
      key: 'messages',
      sorter: (a, b) => a.messages - b.messages
    },
    {
      title: '满意度',
      dataIndex: 'satisfaction',
      key: 'satisfaction',
      render: (score) => (
        <span style={{ color: score >= 4 ? '#52c41a' : '#faad14' }}>
          {score.toFixed(1)}
        </span>
      ),
      sorter: (a, b) => a.satisfaction - b.satisfaction
    }
  ];

  const intentColumns = [
    {
      title: '意图类型',
      dataIndex: 'intent',
      key: 'intent'
    },
    {
      title: '数量',
      dataIndex: 'count',
      key: 'count',
      sorter: (a, b) => a.count - b.count
    },
    {
      title: '占比',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (percentage) => (
        <div>
          <Progress percent={percentage} size="small" />
          <span style={{ marginLeft: 8 }}>{percentage}%</span>
        </div>
      ),
      sorter: (a, b) => a.percentage - b.percentage
    }
  ];

  return (
    <div>
      <Card title="数据分析">
        <Alert
          message="数据分析功能"
          description="提供客户行为分析、满意度统计、对话质量评估等深度分析功能，帮助优化客服质量。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        {/* 筛选条件 */}
        <div style={{ marginBottom: 24 }}>
          <Space>
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              onChange={setDateRange}
            />
            <Select
              placeholder="选择平台"
              style={{ width: 150 }}
              value={platform}
              onChange={setPlatform}
            >
              <Option value="all">全部平台</Option>
              <Option value="wechat_mp">微信公众号</Option>
              <Option value="wechat_work">企业微信</Option>
              <Option value="dingtalk">钉钉</Option>
              <Option value="feishu">飞书</Option>
            </Select>
          </Space>
        </div>

        {/* 核心指标 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="总会话数"
                value={stats.totalSessions}
                prefix={<MessageOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="总消息数"
                value={stats.totalMessages}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="平均响应时间"
                value={stats.avgResponseTime}
                precision={1}
                suffix="秒"
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="客户满意度"
                value={stats.satisfactionScore}
                precision={1}
                suffix="/ 5.0"
                prefix={<SmileOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 性能指标 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12}>
            <Card title="解决率" size="small">
              <Progress
                type="circle"
                percent={stats.resolutionRate}
                format={percent => `${percent}%`}
                strokeColor="#52c41a"
              />
              <div style={{ textAlign: 'center', marginTop: 16 }}>
                <span style={{ color: '#52c41a', fontSize: 16, fontWeight: 'bold' }}>
                  {stats.resolutionRate}%
                </span>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={12}>
            <Card title="升级率" size="small">
              <Progress
                type="circle"
                percent={stats.escalationRate}
                format={percent => `${percent}%`}
                strokeColor="#faad14"
              />
              <div style={{ textAlign: 'center', marginTop: 16 }}>
                <span style={{ color: '#faad14', fontSize: 16, fontWeight: 'bold' }}>
                  {stats.escalationRate}%
                </span>
              </div>
            </Card>
          </Col>
        </Row>

        {/* 平台统计 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col xs={24} lg={12}>
            <Card title="平台统计" size="small">
              <Table
                columns={platformColumns}
                dataSource={platformStats}
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="意图分析" size="small">
              <Table
                columns={intentColumns}
                dataSource={intentStats}
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
        </Row>

        {/* 趋势分析 */}
        <Card title="趋势分析" size="small">
          <Alert
            message="图表功能开发中"
            description="会话趋势、满意度变化、响应时间分布等图表功能正在开发中。"
            type="warning"
            showIcon
          />
        </Card>
      </Card>
    </div>
  );
};

export default AnalyticsDashboard;
