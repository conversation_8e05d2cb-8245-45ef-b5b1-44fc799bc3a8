import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Form,
  Input,
  Select,
  Switch,
  ColorPicker,
  Typography,
  Alert,
  Divider,
  Row,
  Col,
  Modal,
  message,
  Tabs,
  Table,
  Tag,
  Statistic
} from 'antd';
import {
  ShareAltOutlined,
  CopyOutlined,
  EyeOutlined,
  SettingOutlined,
  CodeOutlined,
  BarChartOutlined,
  GlobalOutlined,
  MobileOutlined,
  DesktopOutlined,
  TabletOutlined
} from '@ant-design/icons';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

const PublicWidget = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [embedCodeVisible, setEmbedCodeVisible] = useState(false);
  const [widgetConfig, setWidgetConfig] = useState({
    title: 'AI智能客服',
    welcome_message: '您好！我是AI智能客服，有什么可以帮助您的吗？',
    theme_color: '#1890ff',
    position: 'bottom-right',
    size: 'medium',
    auto_open: false,
    show_avatar: true,
    enable_file_upload: false,
    enable_voice_input: false
  });
  const [widgetStats, setWidgetStats] = useState({
    total_sessions: 0,
    total_messages: 0,
    avg_session_duration: 0,
    user_satisfaction: 0
  });

  // 生成嵌入代码
  const generateEmbedCode = () => {
    const widgetId = 'widget_' + Date.now();
    const baseUrl = window.location.origin;
    
    return `<!-- AI智能客服组件 -->
<script>
(function() {
    var config = {
        widgetId: '${widgetId}',
        theme: 'light',
        size: '${widgetConfig.size}',
        position: '${widgetConfig.position}',
        apiBaseUrl: '${baseUrl}'
    };
    
    var script = document.createElement('script');
    script.src = '${baseUrl}/static/ai-customer-service/widget.js';
    script.onload = function() {
        if (window.AICustomerService) {
            window.AICustomerService.init(config);
        }
    };
    document.head.appendChild(script);
    
    var link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '${baseUrl}/static/ai-customer-service/widget.css';
    document.head.appendChild(link);
})();
</script>`;
  };

  // 复制嵌入代码
  const copyEmbedCode = () => {
    const code = generateEmbedCode();
    navigator.clipboard.writeText(code).then(() => {
      message.success('嵌入代码已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败，请手动复制');
    });
  };

  // 保存配置
  const handleSaveConfig = async (values) => {
    try {
      setLoading(true);
      console.log('保存组件配置:', values);
      setWidgetConfig(values);
      message.success('配置保存成功');
    } catch (error) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据
  const fetchStats = async () => {
    try {
      // 这里调用API获取统计数据
      // 暂时使用模拟数据
      setWidgetStats({
        total_sessions: 156,
        total_messages: 892,
        avg_session_duration: 180,
        user_satisfaction: 4.2
      });
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  useEffect(() => {
    form.setFieldsValue(widgetConfig);
    fetchStats();
  }, []);

  // 预览数据
  const previewData = [
    {
      key: '1',
      device: 'Desktop',
      resolution: '1920x1080',
      sessions: 45,
      icon: <DesktopOutlined />
    },
    {
      key: '2',
      device: 'Mobile',
      resolution: '375x667',
      sessions: 89,
      icon: <MobileOutlined />
    },
    {
      key: '3',
      device: 'Tablet',
      resolution: '768x1024',
      sessions: 22,
      icon: <TabletOutlined />
    }
  ];

  const previewColumns = [
    {
      title: '设备类型',
      key: 'device',
      render: (_, record) => (
        <Space>
          {record.icon}
          <span>{record.device}</span>
        </Space>
      )
    },
    {
      title: '分辨率',
      dataIndex: 'resolution',
      key: 'resolution'
    },
    {
      title: '会话数',
      dataIndex: 'sessions',
      key: 'sessions',
      render: (sessions) => <Tag color="blue">{sessions}</Tag>
    }
  ];

  return (
    <div>
      <Card title="公共客服组件">
        <Alert
          message="公共分享功能"
          description={
            <div>
              <Paragraph>
                生成可嵌入任何网站的客服组件，支持多种分辨率和自定义主题。
                组件会自动适配桌面、平板、手机等各种设备。
              </Paragraph>
              <ul>
                <li>响应式设计，自动适配各种屏幕尺寸</li>
                <li>支持自定义主题颜色、位置、大小</li>
                <li>无需登录即可使用，降低用户使用门槛</li>
                <li>提供详细的使用统计和分析数据</li>
              </ul>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Tabs defaultActiveKey="1">
          <TabPane tab="组件配置" key="1">
            <Row gutter={24}>
              <Col xs={24} lg={12}>
                <Card title="基础配置" size="small">
                  <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleSaveConfig}
                    initialValues={widgetConfig}
                  >
                    <Form.Item
                      name="title"
                      label="组件标题"
                      rules={[{ required: true, message: '请输入组件标题' }]}
                    >
                      <Input placeholder="输入组件标题" />
                    </Form.Item>

                    <Form.Item
                      name="welcome_message"
                      label="欢迎消息"
                      rules={[{ required: true, message: '请输入欢迎消息' }]}
                    >
                      <TextArea
                        rows={3}
                        placeholder="输入欢迎消息"
                        maxLength={200}
                        showCount
                      />
                    </Form.Item>

                    <Form.Item
                      name="theme_color"
                      label="主题颜色"
                    >
                      <ColorPicker
                        value={widgetConfig.theme_color}
                        onChange={(color) => {
                          const newConfig = { ...widgetConfig, theme_color: color.toHexString() };
                          setWidgetConfig(newConfig);
                          form.setFieldValue('theme_color', color.toHexString());
                        }}
                      />
                    </Form.Item>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="position"
                          label="显示位置"
                        >
                          <Select>
                            <Option value="bottom-right">右下角</Option>
                            <Option value="bottom-left">左下角</Option>
                            <Option value="top-right">右上角</Option>
                            <Option value="top-left">左上角</Option>
                          </Select>
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="size"
                          label="组件大小"
                        >
                          <Select>
                            <Option value="small">小</Option>
                            <Option value="medium">中</Option>
                            <Option value="large">大</Option>
                          </Select>
                        </Form.Item>
                      </Col>
                    </Row>

                    <Form.Item
                      name="auto_open"
                      label="自动打开"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item
                      name="show_avatar"
                      label="显示头像"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item
                      name="enable_file_upload"
                      label="启用文件上传"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item
                      name="enable_voice_input"
                      label="启用语音输入"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item>
                      <Space>
                        <Button type="primary" htmlType="submit" loading={loading}>
                          保存配置
                        </Button>
                        <Button onClick={() => setPreviewVisible(true)}>
                          <EyeOutlined /> 预览效果
                        </Button>
                      </Space>
                    </Form.Item>
                  </Form>
                </Card>
              </Col>

              <Col xs={24} lg={12}>
                <Card title="使用统计" size="small">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Statistic
                        title="总会话数"
                        value={widgetStats.total_sessions}
                        prefix={<GlobalOutlined />}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="总消息数"
                        value={widgetStats.total_messages}
                        prefix={<BarChartOutlined />}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="平均会话时长"
                        value={widgetStats.avg_session_duration}
                        suffix="秒"
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="用户满意度"
                        value={widgetStats.user_satisfaction}
                        precision={1}
                        suffix="/ 5.0"
                      />
                    </Col>
                  </Row>

                  <Divider />

                  <div>
                    <Title level={5}>设备分布</Title>
                    <Table
                      columns={previewColumns}
                      dataSource={previewData}
                      pagination={false}
                      size="small"
                    />
                  </div>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="嵌入代码" key="2">
            <Card>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Title level={4}>嵌入代码</Title>
                  <Paragraph>
                    将以下代码复制到您的网站页面中，即可集成AI客服组件。
                    代码会自动加载必要的样式和脚本文件。
                  </Paragraph>
                </div>

                <div style={{ position: 'relative' }}>
                  <TextArea
                    value={generateEmbedCode()}
                    rows={15}
                    readOnly
                    style={{ fontFamily: 'monospace', fontSize: 12 }}
                  />
                  <Button
                    type="primary"
                    icon={<CopyOutlined />}
                    style={{ position: 'absolute', top: 8, right: 8 }}
                    onClick={copyEmbedCode}
                  >
                    复制代码
                  </Button>
                </div>

                <Alert
                  message="使用说明"
                  description={
                    <ul>
                      <li>将代码粘贴到网页的 &lt;head&gt; 或 &lt;body&gt; 标签中</li>
                      <li>组件会自动在页面加载完成后初始化</li>
                      <li>支持在同一页面多次调用，但建议只使用一个实例</li>
                      <li>如需自定义样式，可以通过CSS覆盖默认样式</li>
                    </ul>
                  }
                  type="info"
                  showIcon
                />
              </Space>
            </Card>
          </TabPane>

          <TabPane tab="分析统计" key="3">
            <Card>
              <Title level={4}>详细分析</Title>
              <Paragraph>
                这里将显示公共组件的详细使用分析，包括访问量、转化率、用户行为等数据。
              </Paragraph>
              
              <Alert
                message="功能开发中"
                description="详细的分析统计功能正在开发中，敬请期待。"
                type="warning"
                showIcon
              />
            </Card>
          </TabPane>
        </Tabs>
      </Card>

      {/* 预览弹窗 */}
      <Modal
        title="组件预览"
        visible={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width={800}
      >
        <div style={{ textAlign: 'center', padding: 20 }}>
          <Alert
            message="预览功能"
            description="这里将显示客服组件在不同设备上的预览效果。实际效果请在网站中测试。"
            type="info"
            showIcon
            style={{ marginBottom: 20 }}
          />
          
          <div style={{ 
            border: '1px solid #d9d9d9', 
            borderRadius: 6, 
            padding: 40,
            background: '#f5f5f5',
            minHeight: 300,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Text type="secondary">组件预览区域</Text>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default PublicWidget;
