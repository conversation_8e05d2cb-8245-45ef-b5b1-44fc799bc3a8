import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Select,
  Switch,
  Button,
  Space,
  Typography,
  Alert,
  Divider,
  Row,
  Col,
  Statistic,
  Tag,
  List,
  message,
  Tooltip,
  InputNumber,
  Input
} from 'antd';
import {
  RobotOutlined,
  DatabaseOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { Title, Paragraph, Text } = Typography;

const SystemIntegrationSettings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [aiModels, setAiModels] = useState([]);
  const [knowledgeBases, setKnowledgeBases] = useState([]);
  const [integrationSettings, setIntegrationSettings] = useState({
    ai_model_id: null,
    knowledge_base_ids: [],
    temperature: 0.7,
    max_tokens: 2000,
    enable_context_memory: true,
    enable_knowledge_search: true,
    confidence_threshold: 0.8,
    fallback_to_human: true
  });

  // 获取项目ID
  const getProjectId = () => {
    const projectId = localStorage.getItem('project_id');
    if (projectId && projectId !== 'null' && projectId !== 'undefined') {
      return projectId;
    }
    
    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    if (userData && userData.project_id) {
      localStorage.setItem('project_id', userData.project_id);
      return userData.project_id;
    }
    
    return null;
  };

  // 获取AI模型列表
  const fetchAiModels = async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) return;

      const response = await fetch(`/api/project/${projectId}/ai/models`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAiModels(data.data || []);
      }
    } catch (error) {
      console.error('获取AI模型失败:', error);
    }
  };

  // 获取知识库列表
  const fetchKnowledgeBases = async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) return;

      const response = await fetch(`/api/project/${projectId}/ai/knowledge/bases`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setKnowledgeBases(data.data || []);
      }
    } catch (error) {
      console.error('获取知识库失败:', error);
    }
  };

  // 获取系统整合设置
  const fetchIntegrationSettings = async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) return;

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/integration/settings`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setIntegrationSettings(data);
        form.setFieldsValue(data);
      }
    } catch (error) {
      console.error('获取系统整合设置失败:', error);
    }
  };

  // 保存系统整合设置
  const handleSaveSettings = async (values) => {
    try {
      setLoading(true);
      const projectId = getProjectId();
      if (!projectId) {
        message.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/integration/settings`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(values)
      });

      if (response.ok) {
        setIntegrationSettings(values);
        message.success('系统整合设置保存成功');
      } else {
        message.error('保存系统整合设置失败');
      }
    } catch (error) {
      console.error('保存系统整合设置失败:', error);
      message.error('保存系统整合设置失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAiModels();
    fetchKnowledgeBases();
    fetchIntegrationSettings();
  }, []);

  return (
    <div>
      <Alert
        message="系统整合配置"
        description="配置AI客服与系统中AI模型和知识库的整合设置，确保客服能够使用最合适的AI能力。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Row gutter={24}>
        <Col xs={24} lg={16}>
          <Card title="整合设置" size="small">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSaveSettings}
              initialValues={integrationSettings}
            >
              <Form.Item
                name="ai_model_id"
                label={
                  <Space>
                    <RobotOutlined />
                    <span>AI模型</span>
                    <Tooltip title="选择用于客服对话的AI模型">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: '请选择AI模型' }]}
              >
                <Select placeholder="选择AI模型" allowClear>
                  {aiModels.map(model => (
                    <Option key={model.id} value={model.id}>
                      <Space>
                        <span>{model.name}</span>
                        <Tag color={model.status === 'active' ? 'green' : 'orange'}>
                          {model.provider}
                        </Tag>
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="knowledge_base_ids"
                label={
                  <Space>
                    <DatabaseOutlined />
                    <span>知识库</span>
                    <Tooltip title="选择客服可以使用的知识库">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                }
              >
                <Select 
                  mode="multiple" 
                  placeholder="选择知识库" 
                  allowClear
                  maxTagCount={3}
                >
                  {knowledgeBases.map(kb => (
                    <Option key={kb.id} value={kb.id}>
                      <Space>
                        <span>{kb.name}</span>
                        <Tag color="blue">{kb.type}</Tag>
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="temperature"
                    label={
                      <Space>
                        <span>温度参数</span>
                        <Tooltip title="控制AI回复的创造性，0-1之间，越高越有创造性">
                          <InfoCircleOutlined />
                        </Tooltip>
                      </Space>
                    }
                  >
                    <InputNumber
                      min={0}
                      max={1}
                      step={0.1}
                      style={{ width: '100%' }}
                      placeholder="0.7"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="max_tokens"
                    label={
                      <Space>
                        <span>最大令牌数</span>
                        <Tooltip title="限制AI回复的最大长度">
                          <InfoCircleOutlined />
                        </Tooltip>
                      </Space>
                    }
                  >
                    <InputNumber
                      min={100}
                      max={4000}
                      step={100}
                      style={{ width: '100%' }}
                      placeholder="2000"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="confidence_threshold"
                label={
                  <Space>
                    <span>置信度阈值</span>
                    <Tooltip title="AI回复置信度低于此值时将转人工">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                }
              >
                <InputNumber
                  min={0}
                  max={1}
                  step={0.1}
                  style={{ width: '100%' }}
                  placeholder="0.8"
                />
              </Form.Item>

              <Divider />

              <Form.Item
                name="enable_context_memory"
                label="启用上下文记忆"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="enable_knowledge_search"
                label="启用知识库搜索"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="fallback_to_human"
                label="自动转人工"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading}>
                  保存整合设置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="整合状态" size="small" style={{ marginBottom: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Statistic
                title="可用AI模型"
                value={aiModels.length}
                prefix={<RobotOutlined />}
                valueStyle={{ color: aiModels.length > 0 ? '#3f8600' : '#cf1322' }}
              />
              <Statistic
                title="可用知识库"
                value={knowledgeBases.length}
                prefix={<DatabaseOutlined />}
                valueStyle={{ color: knowledgeBases.length > 0 ? '#3f8600' : '#cf1322' }}
              />
            </Space>
          </Card>

          <Card title="配置建议" size="small">
            <List
              size="small"
              dataSource={[
                {
                  icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
                  text: '选择性能最佳的AI模型'
                },
                {
                  icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
                  text: '配置相关业务知识库'
                },
                {
                  icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
                  text: '设置合适的置信度阈值'
                },
                {
                  icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />,
                  text: '启用上下文记忆提升体验'
                }
              ]}
              renderItem={item => (
                <List.Item>
                  <Space>
                    {item.icon}
                    <Text>{item.text}</Text>
                  </Space>
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SystemIntegrationSettings;
