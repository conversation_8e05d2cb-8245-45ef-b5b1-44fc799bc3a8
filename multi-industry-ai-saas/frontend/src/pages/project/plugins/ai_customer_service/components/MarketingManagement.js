import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Statistic,
  Row,
  Col,
  Alert,
  Typography,
  Progress,
  List,
  Badge
} from 'antd';
import {
  ThunderboltOutlined,
  GiftOutlined,
  UserOutlined,
  TrophyOutlined,
  RiseOutlined,
  ReloadOutlined
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;

const MarketingManagement = () => {
  const [loading, setLoading] = useState(false);
  const [marketingStats, setMarketingStats] = useState({
    totalOpportunities: 0,
    convertedOpportunities: 0,
    totalCoupons: 0,
    totalRecommendations: 0
  });
  const [opportunities, setOpportunities] = useState([]);
  const [campaigns, setCampaigns] = useState([]);

  // 获取营销统计数据
  const fetchMarketingStats = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      const mockStats = {
        totalOpportunities: 156,
        convertedOpportunities: 89,
        totalCoupons: 234,
        totalRecommendations: 567
      };
      
      setMarketingStats(mockStats);
    } catch (error) {
      console.error('获取营销统计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取营销机会
  const fetchOpportunities = async () => {
    try {
      // 模拟API调用
      const mockOpportunities = [
        {
          id: '1',
          customer_name: '张三',
          opportunity_type: 'upsell',
          product_interest: 'SaaS高级版',
          confidence_score: 0.85,
          estimated_value: 5000,
          status: 'active',
          created_at: '2025-01-08 10:30:00'
        },
        {
          id: '2',
          customer_name: '李四',
          opportunity_type: 'cross_sell',
          product_interest: 'AI助手插件',
          confidence_score: 0.72,
          estimated_value: 2000,
          status: 'converted',
          created_at: '2025-01-08 09:15:00'
        },
        {
          id: '3',
          customer_name: '王五',
          opportunity_type: 'retention',
          product_interest: '续费优惠',
          confidence_score: 0.68,
          estimated_value: 3000,
          status: 'pending',
          created_at: '2025-01-08 08:00:00'
        }
      ];
      
      setOpportunities(mockOpportunities);
    } catch (error) {
      console.error('获取营销机会失败:', error);
    }
  };

  // 获取营销活动
  const fetchCampaigns = async () => {
    try {
      // 模拟API调用
      const mockCampaigns = [
        {
          id: '1',
          name: '新用户优惠券',
          type: 'coupon',
          target_audience: '新注册用户',
          sent_count: 156,
          opened_count: 89,
          converted_count: 23,
          conversion_rate: 14.7,
          status: 'active'
        },
        {
          id: '2',
          name: '产品推荐',
          type: 'recommendation',
          target_audience: '活跃用户',
          sent_count: 234,
          opened_count: 167,
          converted_count: 45,
          conversion_rate: 19.2,
          status: 'active'
        },
        {
          id: '3',
          name: '续费提醒',
          type: 'retention',
          target_audience: '即将到期用户',
          sent_count: 89,
          opened_count: 67,
          converted_count: 34,
          conversion_rate: 38.2,
          status: 'completed'
        }
      ];
      
      setCampaigns(mockCampaigns);
    } catch (error) {
      console.error('获取营销活动失败:', error);
    }
  };

  // 获取机会类型标签
  const getOpportunityTypeTag = (type) => {
    const typeMap = {
      'upsell': { color: 'green', text: '升级销售' },
      'cross_sell': { color: 'blue', text: '交叉销售' },
      'retention': { color: 'orange', text: '客户留存' }
    };
    
    const typeInfo = typeMap[type] || { color: 'default', text: type };
    return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>;
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    const statusMap = {
      'active': { color: 'processing', text: '进行中' },
      'converted': { color: 'success', text: '已转化' },
      'pending': { color: 'warning', text: '待处理' },
      'completed': { color: 'default', text: '已完成' }
    };
    
    const statusInfo = statusMap[status] || { color: 'default', text: status };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  // 获取活动类型标签
  const getCampaignTypeTag = (type) => {
    const typeMap = {
      'coupon': { color: 'red', text: '优惠券' },
      'recommendation': { color: 'blue', text: '产品推荐' },
      'retention': { color: 'purple', text: '留存营销' }
    };
    
    const typeInfo = typeMap[type] || { color: 'default', text: type };
    return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>;
  };

  useEffect(() => {
    fetchMarketingStats();
    fetchOpportunities();
    fetchCampaigns();
  }, []);

  const opportunityColumns = [
    {
      title: '客户',
      dataIndex: 'customer_name',
      key: 'customer_name',
      render: (name) => (
        <Space>
          <UserOutlined />
          <span>{name}</span>
        </Space>
      )
    },
    {
      title: '机会类型',
      dataIndex: 'opportunity_type',
      key: 'opportunity_type',
      render: (type) => getOpportunityTypeTag(type)
    },
    {
      title: '产品兴趣',
      dataIndex: 'product_interest',
      key: 'product_interest'
    },
    {
      title: '置信度',
      dataIndex: 'confidence_score',
      key: 'confidence_score',
      render: (score) => (
        <div>
          <Progress percent={Math.round(score * 100)} size="small" />
          <Text style={{ fontSize: 12 }}>{(score * 100).toFixed(0)}%</Text>
        </div>
      )
    },
    {
      title: '预估价值',
      dataIndex: 'estimated_value',
      key: 'estimated_value',
      render: (value) => `¥${value.toLocaleString()}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status)
    }
  ];

  const campaignColumns = [
    {
      title: '活动名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => getCampaignTypeTag(type)
    },
    {
      title: '目标受众',
      dataIndex: 'target_audience',
      key: 'target_audience'
    },
    {
      title: '发送数',
      dataIndex: 'sent_count',
      key: 'sent_count',
      render: (count) => <Badge count={count} showZero />
    },
    {
      title: '打开数',
      dataIndex: 'opened_count',
      key: 'opened_count',
      render: (count) => <Badge count={count} showZero style={{ backgroundColor: '#52c41a' }} />
    },
    {
      title: '转化数',
      dataIndex: 'converted_count',
      key: 'converted_count',
      render: (count) => <Badge count={count} showZero style={{ backgroundColor: '#1890ff' }} />
    },
    {
      title: '转化率',
      dataIndex: 'conversion_rate',
      key: 'conversion_rate',
      render: (rate) => `${rate}%`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status)
    }
  ];

  return (
    <div>
      <Card title="智能营销管理">
        <Alert
          message="智能营销功能"
          description="基于客户行为分析和AI算法，自动识别营销机会，推送个性化优惠券和产品推荐，提升转化率和客户价值。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        {/* 营销统计 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="营销机会"
                value={marketingStats.totalOpportunities}
                prefix={<ThunderboltOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="转化机会"
                value={marketingStats.convertedOpportunities}
                prefix={<TrophyOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="优惠券发放"
                value={marketingStats.totalCoupons}
                prefix={<GiftOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="产品推荐"
                value={marketingStats.totalRecommendations}
                prefix={<RiseOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 转化率统计 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={12}>
            <Card title="机会转化率" size="small">
              <Progress
                type="circle"
                percent={Math.round((marketingStats.convertedOpportunities / marketingStats.totalOpportunities) * 100)}
                format={percent => `${percent}%`}
                strokeColor="#52c41a"
              />
              <div style={{ textAlign: 'center', marginTop: 16 }}>
                <Text type="secondary">
                  {marketingStats.convertedOpportunities} / {marketingStats.totalOpportunities}
                </Text>
              </div>
            </Card>
          </Col>
          <Col span={12}>
            <Card title="营销效果" size="small">
              <List
                size="small"
                dataSource={[
                  { label: '平均转化率', value: '18.5%', color: '#52c41a' },
                  { label: '客户生命周期价值', value: '+25%', color: '#1890ff' },
                  { label: '复购率', value: '32%', color: '#722ed1' },
                  { label: '推荐成功率', value: '15.8%', color: '#faad14' }
                ]}
                renderItem={item => (
                  <List.Item>
                    <Space>
                      <Text>{item.label}:</Text>
                      <Text style={{ color: item.color, fontWeight: 'bold' }}>
                        {item.value}
                      </Text>
                    </Space>
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        </Row>

        {/* 营销机会列表 */}
        <Card 
          title="营销机会" 
          size="small" 
          style={{ marginBottom: 16 }}
          extra={
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchOpportunities}
              size="small"
            >
              刷新
            </Button>
          }
        >
          <Table
            columns={opportunityColumns}
            dataSource={opportunities}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </Card>

        {/* 营销活动列表 */}
        <Card 
          title="营销活动" 
          size="small"
          extra={
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchCampaigns}
              size="small"
            >
              刷新
            </Button>
          }
        >
          <Table
            columns={campaignColumns}
            dataSource={campaigns}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </Card>
      </Card>
    </div>
  );
};

export default MarketingManagement;
