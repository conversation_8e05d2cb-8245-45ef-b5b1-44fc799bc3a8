import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  DatePicker,
  Modal,
  Descriptions,
  List,
  Avatar,
  Typography,
  message,
  Tooltip,
  Badge
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  MessageOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Text } = Typography;

const SessionManagement = () => {
  const [loading, setLoading] = useState(false);
  const [sessions, setSessions] = useState([]);
  const [filteredSessions, setFilteredSessions] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [platformFilter, setPlatformFilter] = useState('all');
  const [dateRange, setDateRange] = useState([]);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedSession, setSelectedSession] = useState(null);
  const [messages, setMessages] = useState([]);

  // 获取会话列表
  const fetchSessions = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/project/plugins/ai-customer-service/sessions', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSessions(data.sessions || []);
        setFilteredSessions(data.sessions || []);
      } else {
        setSessions([]);
        setFilteredSessions([]);
        message.warning('暂无会话数据');
      }
    } catch (error) {
      console.error('获取会话列表失败:', error);
      setSessions([]);
      setFilteredSessions([]);
      message.error('获取会话列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 过滤会话
  const filterSessions = () => {
    let filtered = [...sessions];

    // 按搜索文本过滤
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(session =>
        session.user_name.toLowerCase().includes(searchLower) ||
        session.title.toLowerCase().includes(searchLower) ||
        session.session_id.toLowerCase().includes(searchLower)
      );
    }

    // 按状态过滤
    if (statusFilter !== 'all') {
      filtered = filtered.filter(session => session.status === statusFilter);
    }

    // 按平台过滤
    if (platformFilter !== 'all') {
      filtered = filtered.filter(session => session.platform_type === platformFilter);
    }

    // 按日期范围过滤
    if (dateRange.length === 2) {
      const [startDate, endDate] = dateRange;
      filtered = filtered.filter(session => {
        const sessionDate = dayjs(session.started_at);
        return sessionDate.isBetween(startDate, endDate, 'day', '[]');
      });
    }

    setFilteredSessions(filtered);
  };

  // 查看会话详情
  const handleViewSession = async (session) => {
    setSelectedSession(session);
    setDetailModalVisible(true);

    // 获取会话消息
    try {
      const response = await fetch(`/api/v1/project/plugins/ai-customer-service/sessions/${session.session_id}/messages`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setMessages(data.messages || []);
      } else {
        setMessages([]);
        message.warning('暂无消息记录');
      }
    } catch (error) {
      console.error('获取消息失败:', error);
      setMessages([]);
      message.error('获取消息失败');
    }
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    const statusMap = {
      'active': { color: 'processing', text: '进行中' },
      'resolved': { color: 'success', text: '已解决' },
      'escalated': { color: 'warning', text: '已升级' },
      'closed': { color: 'default', text: '已关闭' }
    };
    
    const statusInfo = statusMap[status] || { color: 'default', text: status };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  // 获取平台标签
  const getPlatformTag = (platform) => {
    const platformMap = {
      'wechat_mp': { color: 'green', text: '微信公众号' },
      'wechat_work': { color: 'blue', text: '企业微信' },
      'dingtalk': { color: 'orange', text: '钉钉' },
      'feishu': { color: 'purple', text: '飞书' }
    };
    
    const platformInfo = platformMap[platform] || { color: 'default', text: platform };
    return <Tag color={platformInfo.color}>{platformInfo.text}</Tag>;
  };

  // 获取满意度显示
  const getSatisfactionDisplay = (score) => {
    if (!score) return '-';
    
    const color = score >= 4 ? '#52c41a' : score >= 3 ? '#faad14' : '#f5222d';
    return <Text style={{ color }}>{score.toFixed(1)}</Text>;
  };

  useEffect(() => {
    fetchSessions();
  }, []);

  useEffect(() => {
    filterSessions();
  }, [searchText, statusFilter, platformFilter, dateRange, sessions]);

  const columns = [
    {
      title: '会话ID',
      dataIndex: 'session_id',
      key: 'session_id',
      width: 120,
      render: (text) => <Text code>{text}</Text>
    },
    {
      title: '用户',
      key: 'user',
      width: 120,
      render: (_, record) => (
        <Space>
          <Avatar icon={<UserOutlined />} size="small" />
          <span>{record.user_name}</span>
        </Space>
      )
    },
    {
      title: '平台',
      dataIndex: 'platform_type',
      key: 'platform_type',
      width: 100,
      render: (platform) => getPlatformTag(platform)
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => getStatusTag(status)
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true
    },
    {
      title: '消息数',
      dataIndex: 'message_count',
      key: 'message_count',
      width: 80,
      render: (count) => <Badge count={count} showZero />
    },
    {
      title: '满意度',
      dataIndex: 'satisfaction_score',
      key: 'satisfaction_score',
      width: 80,
      render: (score) => getSatisfactionDisplay(score)
    },
    {
      title: '人工介入',
      dataIndex: 'human_takeover_count',
      key: 'human_takeover_count',
      width: 80,
      render: (count) => count > 0 ? <Badge count={count} /> : '-'
    },
    {
      title: '开始时间',
      dataIndex: 'started_at',
      key: 'started_at',
      width: 150,
      render: (time) => dayjs(time).format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewSession(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div>
      <Card
        title="会话管理"
        extra={
          <Button
            icon={<ReloadOutlined />}
            onClick={fetchSessions}
            loading={loading}
          >
            刷新
          </Button>
        }
      >
        {/* 筛选条件 */}
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Search
              placeholder="搜索用户名、标题或会话ID"
              allowClear
              style={{ width: 250 }}
              onSearch={setSearchText}
            />
            <Select
              placeholder="状态"
              style={{ width: 120 }}
              value={statusFilter}
              onChange={setStatusFilter}
            >
              <Option value="all">全部状态</Option>
              <Option value="active">进行中</Option>
              <Option value="resolved">已解决</Option>
              <Option value="escalated">已升级</Option>
              <Option value="closed">已关闭</Option>
            </Select>
            <Select
              placeholder="平台"
              style={{ width: 120 }}
              value={platformFilter}
              onChange={setPlatformFilter}
            >
              <Option value="all">全部平台</Option>
              <Option value="wechat_mp">微信公众号</Option>
              <Option value="wechat_work">企业微信</Option>
              <Option value="dingtalk">钉钉</Option>
              <Option value="feishu">飞书</Option>
            </Select>
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              onChange={setDateRange}
            />
          </Space>
        </div>

        {/* 会话列表 */}
        <Table
          columns={columns}
          dataSource={filteredSessions}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredSessions.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 会话详情弹窗 */}
      <Modal
        title="会话详情"
        visible={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedSession && (
          <div>
            <Descriptions bordered size="small" column={2}>
              <Descriptions.Item label="会话ID">{selectedSession.session_id}</Descriptions.Item>
              <Descriptions.Item label="用户">{selectedSession.user_name}</Descriptions.Item>
              <Descriptions.Item label="平台">{getPlatformTag(selectedSession.platform_type)}</Descriptions.Item>
              <Descriptions.Item label="状态">{getStatusTag(selectedSession.status)}</Descriptions.Item>
              <Descriptions.Item label="开始时间">{selectedSession.started_at}</Descriptions.Item>
              <Descriptions.Item label="最后活动">{selectedSession.last_activity_at}</Descriptions.Item>
              <Descriptions.Item label="消息数量">{selectedSession.message_count}</Descriptions.Item>
              <Descriptions.Item label="满意度">{getSatisfactionDisplay(selectedSession.satisfaction_score)}</Descriptions.Item>
              <Descriptions.Item label="人工介入次数">{selectedSession.human_takeover_count}</Descriptions.Item>
              <Descriptions.Item label="AI模型">{selectedSession.ai_model_used}</Descriptions.Item>
            </Descriptions>

            <div style={{ marginTop: 16 }}>
              <h4>对话记录</h4>
              <List
                dataSource={messages}
                renderItem={message => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Avatar 
                          icon={message.is_from_user ? <UserOutlined /> : <MessageOutlined />}
                          style={{ 
                            backgroundColor: message.is_from_user ? '#1890ff' : '#52c41a' 
                          }}
                        />
                      }
                      title={
                        <Space>
                          <span>{message.sender_name}</span>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            {dayjs(message.created_at).format('HH:mm:ss')}
                          </Text>
                          {!message.is_from_user && message.confidence_score && (
                            <Tag color="blue">置信度: {(message.confidence_score * 100).toFixed(0)}%</Tag>
                          )}
                        </Space>
                      }
                      description={
                        <div style={{ whiteSpace: 'pre-wrap' }}>
                          {message.content}
                        </div>
                      }
                    />
                  </List.Item>
                )}
                style={{ maxHeight: 400, overflow: 'auto' }}
              />
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default SessionManagement;
