import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  DatePicker,
  Modal,
  Form,
  message,
  Typography,
  Badge,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  UserOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Text } = Typography;

const TicketManagement = () => {
  const [loading, setLoading] = useState(false);
  const [tickets, setTickets] = useState([]);
  const [filteredTickets, setFilteredTickets] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [dateRange, setDateRange] = useState([]);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);

  // 获取工单列表
  const fetchTickets = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      const mockTickets = [
        {
          id: '1',
          ticket_id: 'TK001',
          title: '客户投诉产品质量问题',
          description: '客户反映产品存在质量问题，要求退款处理',
          status: 'open',
          priority: 'high',
          category: 'complaint',
          session_id: 'session_001',
          customer_name: '张三',
          assignee: '客服经理',
          created_at: '2025-01-08 10:30:00',
          updated_at: '2025-01-08 11:00:00',
          due_date: '2025-01-08 12:30:00',
          resolution_notes: null
        },
        {
          id: '2',
          ticket_id: 'TK002',
          title: '合作伙伴咨询',
          description: '潜在合作伙伴咨询合作模式和条件',
          status: 'in_progress',
          priority: 'medium',
          category: 'partnership',
          session_id: 'session_002',
          customer_name: '李四',
          assignee: '商务经理',
          created_at: '2025-01-08 09:15:00',
          updated_at: '2025-01-08 10:45:00',
          due_date: '2025-01-09 09:15:00',
          resolution_notes: '已安排商务经理跟进'
        },
        {
          id: '3',
          ticket_id: 'TK003',
          title: '技术支持请求',
          description: '客户在使用过程中遇到技术问题',
          status: 'resolved',
          priority: 'medium',
          category: 'technical_support',
          session_id: 'session_003',
          customer_name: '王五',
          assignee: '技术支持',
          created_at: '2025-01-08 08:00:00',
          updated_at: '2025-01-08 09:30:00',
          due_date: '2025-01-08 16:00:00',
          resolution_notes: '问题已解决，客户满意'
        }
      ];
      
      setTickets(mockTickets);
      setFilteredTickets(mockTickets);
    } catch (error) {
      console.error('获取工单列表失败:', error);
      message.error('获取工单列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 过滤工单
  const filterTickets = () => {
    let filtered = [...tickets];

    // 按搜索文本过滤
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(ticket =>
        ticket.title.toLowerCase().includes(searchLower) ||
        ticket.customer_name.toLowerCase().includes(searchLower) ||
        ticket.ticket_id.toLowerCase().includes(searchLower)
      );
    }

    // 按状态过滤
    if (statusFilter !== 'all') {
      filtered = filtered.filter(ticket => ticket.status === statusFilter);
    }

    // 按优先级过滤
    if (priorityFilter !== 'all') {
      filtered = filtered.filter(ticket => ticket.priority === priorityFilter);
    }

    // 按日期范围过滤
    if (dateRange.length === 2) {
      const [startDate, endDate] = dateRange;
      filtered = filtered.filter(ticket => {
        const ticketDate = dayjs(ticket.created_at);
        return ticketDate.isBetween(startDate, endDate, 'day', '[]');
      });
    }

    setFilteredTickets(filtered);
  };

  // 查看工单详情
  const handleViewTicket = (ticket) => {
    setSelectedTicket(ticket);
    setDetailModalVisible(true);
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    const statusMap = {
      'open': { color: 'red', text: '待处理' },
      'in_progress': { color: 'orange', text: '处理中' },
      'resolved': { color: 'green', text: '已解决' },
      'closed': { color: 'default', text: '已关闭' }
    };
    
    const statusInfo = statusMap[status] || { color: 'default', text: status };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  // 获取优先级标签
  const getPriorityTag = (priority) => {
    const priorityMap = {
      'high': { color: 'red', text: '高' },
      'medium': { color: 'orange', text: '中' },
      'low': { color: 'green', text: '低' }
    };
    
    const priorityInfo = priorityMap[priority] || { color: 'default', text: priority };
    return <Tag color={priorityInfo.color}>{priorityInfo.text}</Tag>;
  };

  // 获取分类标签
  const getCategoryTag = (category) => {
    const categoryMap = {
      'complaint': { color: 'red', text: '投诉' },
      'partnership': { color: 'blue', text: '合作' },
      'technical_support': { color: 'purple', text: '技术支持' },
      'sales_inquiry': { color: 'green', text: '销售咨询' }
    };
    
    const categoryInfo = categoryMap[category] || { color: 'default', text: category };
    return <Tag color={categoryInfo.color}>{categoryInfo.text}</Tag>;
  };

  // 检查是否逾期
  const isOverdue = (dueDate, status) => {
    if (status === 'resolved' || status === 'closed') return false;
    return dayjs().isAfter(dayjs(dueDate));
  };

  useEffect(() => {
    fetchTickets();
  }, []);

  useEffect(() => {
    filterTickets();
  }, [searchText, statusFilter, priorityFilter, dateRange, tickets]);

  const columns = [
    {
      title: '工单号',
      dataIndex: 'ticket_id',
      key: 'ticket_id',
      width: 100,
      render: (text) => <Text code>{text}</Text>
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      render: (text, record) => (
        <div>
          <div>{text}</div>
          {isOverdue(record.due_date, record.status) && (
            <Badge status="error" text="逾期" />
          )}
        </div>
      )
    },
    {
      title: '客户',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 100,
      render: (name) => (
        <Space>
          <UserOutlined />
          <span>{name}</span>
        </Space>
      )
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category) => getCategoryTag(category)
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => getStatusTag(status)
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority) => getPriorityTag(priority)
    },
    {
      title: '负责人',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 100
    },
    {
      title: '截止时间',
      dataIndex: 'due_date',
      key: 'due_date',
      width: 150,
      render: (time, record) => (
        <div>
          <ClockCircleOutlined style={{ 
            color: isOverdue(time, record.status) ? '#f5222d' : '#1890ff' 
          }} />
          <span style={{ marginLeft: 4 }}>
            {dayjs(time).format('MM-DD HH:mm')}
          </span>
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewTicket(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div>
      <Card
        title="工单管理"
        extra={
          <Button
            icon={<ReloadOutlined />}
            onClick={fetchTickets}
            loading={loading}
          >
            刷新
          </Button>
        }
      >
        {/* 筛选条件 */}
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Search
              placeholder="搜索工单号、标题或客户"
              allowClear
              style={{ width: 250 }}
              onSearch={setSearchText}
            />
            <Select
              placeholder="状态"
              style={{ width: 120 }}
              value={statusFilter}
              onChange={setStatusFilter}
            >
              <Option value="all">全部状态</Option>
              <Option value="open">待处理</Option>
              <Option value="in_progress">处理中</Option>
              <Option value="resolved">已解决</Option>
              <Option value="closed">已关闭</Option>
            </Select>
            <Select
              placeholder="优先级"
              style={{ width: 120 }}
              value={priorityFilter}
              onChange={setPriorityFilter}
            >
              <Option value="all">全部优先级</Option>
              <Option value="high">高</Option>
              <Option value="medium">中</Option>
              <Option value="low">低</Option>
            </Select>
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              onChange={setDateRange}
            />
          </Space>
        </div>

        {/* 工单列表 */}
        <Table
          columns={columns}
          dataSource={filteredTickets}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredTickets.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 工单详情弹窗 */}
      <Modal
        title="工单详情"
        visible={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedTicket && (
          <div>
            <Card size="small" style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <Text strong style={{ fontSize: 16 }}>{selectedTicket.title}</Text>
                  <div style={{ marginTop: 8 }}>
                    <Space>
                      <Text code>{selectedTicket.ticket_id}</Text>
                      {getStatusTag(selectedTicket.status)}
                      {getPriorityTag(selectedTicket.priority)}
                      {getCategoryTag(selectedTicket.category)}
                    </Space>
                  </div>
                </div>
                <div style={{ textAlign: 'right' }}>
                  <div>负责人：{selectedTicket.assignee}</div>
                  <div>截止时间：{dayjs(selectedTicket.due_date).format('YYYY-MM-DD HH:mm')}</div>
                </div>
              </div>
            </Card>

            <Card title="工单描述" size="small" style={{ marginBottom: 16 }}>
              <Text>{selectedTicket.description}</Text>
            </Card>

            <Card title="客户信息" size="small" style={{ marginBottom: 16 }}>
              <Space direction="vertical">
                <div>客户姓名：{selectedTicket.customer_name}</div>
                <div>关联会话：{selectedTicket.session_id}</div>
                <div>创建时间：{selectedTicket.created_at}</div>
                <div>更新时间：{selectedTicket.updated_at}</div>
              </Space>
            </Card>

            {selectedTicket.resolution_notes && (
              <Card title="处理记录" size="small">
                <Text>{selectedTicket.resolution_notes}</Text>
              </Card>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default TicketManagement;
