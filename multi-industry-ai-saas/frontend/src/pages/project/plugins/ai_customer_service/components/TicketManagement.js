import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  DatePicker,
  Modal,
  Form,
  message,
  Typography,
  Badge,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  PlusOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Text } = Typography;

const TicketManagement = () => {
  const [loading, setLoading] = useState(false);
  const [tickets, setTickets] = useState([]);
  const [filteredTickets, setFilteredTickets] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [dateRange, setDateRange] = useState([]);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [form] = Form.useForm();

  // 获取项目ID
  const getProjectId = () => {
    const projectId = localStorage.getItem('project_id');
    if (projectId && projectId !== 'null' && projectId !== 'undefined') {
      return projectId;
    }

    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    if (userData && userData.project_id) {
      localStorage.setItem('project_id', userData.project_id);
      return userData.project_id;
    }

    return null;
  };

  // 获取工单列表
  const fetchTickets = async () => {
    setLoading(true);
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        setLoading(false);
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugins/ai-customer-service/tickets`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTickets(data.tickets || []);
        setFilteredTickets(data.tickets || []);
      } else {
        // 如果API调用失败，使用模拟数据
        const mockTickets = [
        {
          id: '1',
          ticket_id: 'TK001',
          title: '客户投诉产品质量问题',
          description: '客户反映产品存在质量问题，要求退款处理',
          status: 'open',
          priority: 'high',
          category: 'complaint',
          session_id: 'session_001',
          customer_name: '张三',
          assignee: '客服经理',
          created_at: '2025-01-08 10:30:00',
          updated_at: '2025-01-08 11:00:00',
          due_date: '2025-01-08 12:30:00',
          resolution_notes: null
        },
        {
          id: '2',
          ticket_id: 'TK002',
          title: '合作伙伴咨询',
          description: '潜在合作伙伴咨询合作模式和条件',
          status: 'in_progress',
          priority: 'medium',
          category: 'partnership',
          session_id: 'session_002',
          customer_name: '李四',
          assignee: '商务经理',
          created_at: '2025-01-08 09:15:00',
          updated_at: '2025-01-08 10:45:00',
          due_date: '2025-01-09 09:15:00',
          resolution_notes: '已安排商务经理跟进'
        },
        {
          id: '3',
          ticket_id: 'TK003',
          title: '技术支持请求',
          description: '客户在使用过程中遇到技术问题',
          status: 'resolved',
          priority: 'medium',
          category: 'technical_support',
          session_id: 'session_003',
          customer_name: '王五',
          assignee: '技术支持',
          created_at: '2025-01-08 08:00:00',
          updated_at: '2025-01-08 09:30:00',
          due_date: '2025-01-08 16:00:00',
          resolution_notes: '问题已解决，客户满意'
        }
        ];

        setTickets(mockTickets);
        setFilteredTickets(mockTickets);
      }
    } catch (error) {
      console.error('获取工单列表失败:', error);
      message.error('获取工单列表失败');
      setTickets([]);
      setFilteredTickets([]);
    } finally {
      setLoading(false);
    }
  };

  // 过滤工单
  const filterTickets = () => {
    let filtered = [...tickets];

    // 按搜索文本过滤
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(ticket =>
        ticket.title.toLowerCase().includes(searchLower) ||
        ticket.customer_name.toLowerCase().includes(searchLower) ||
        ticket.ticket_id.toLowerCase().includes(searchLower)
      );
    }

    // 按状态过滤
    if (statusFilter !== 'all') {
      filtered = filtered.filter(ticket => ticket.status === statusFilter);
    }

    // 按优先级过滤
    if (priorityFilter !== 'all') {
      filtered = filtered.filter(ticket => ticket.priority === priorityFilter);
    }

    // 按日期范围过滤
    if (dateRange.length === 2) {
      const [startDate, endDate] = dateRange;
      filtered = filtered.filter(ticket => {
        const ticketDate = dayjs(ticket.created_at);
        return ticketDate.isBetween(startDate, endDate, 'day', '[]');
      });
    }

    setFilteredTickets(filtered);
  };

  // 查看工单详情
  const handleViewTicket = (ticket) => {
    setSelectedTicket(ticket);
    setDetailModalVisible(true);
  };

  // 编辑工单
  const handleEditTicket = (ticket) => {
    setSelectedTicket(ticket);
    form.setFieldsValue({
      title: ticket.title,
      description: ticket.description,
      status: ticket.status,
      priority: ticket.priority,
      category: ticket.category,
      assignee: ticket.assignee,
      due_date: dayjs(ticket.due_date),
      resolution_notes: ticket.resolution_notes
    });
    setEditModalVisible(true);
  };

  // 创建工单
  const handleCreateTicket = () => {
    form.resetFields();
    setSelectedTicket(null);
    setCreateModalVisible(true);
  };

  // 保存工单
  const handleSaveTicket = async (values) => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        message.error('无法获取项目ID');
        return;
      }

      const ticketData = {
        ...values,
        due_date: values.due_date ? values.due_date.format('YYYY-MM-DD HH:mm:ss') : null
      };

      let response;
      if (selectedTicket) {
        // 更新工单
        response = await fetch(`/api/v1/project/${projectId}/plugins/ai-customer-service/tickets/${selectedTicket.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(ticketData)
        });
      } else {
        // 创建工单
        response = await fetch(`/api/v1/project/${projectId}/plugins/ai-customer-service/tickets`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(ticketData)
        });
      }

      if (response.ok) {
        message.success(selectedTicket ? '工单更新成功' : '工单创建成功');
        setEditModalVisible(false);
        setCreateModalVisible(false);
        fetchTickets();
      } else {
        message.error(selectedTicket ? '工单更新失败' : '工单创建失败');
      }
    } catch (error) {
      console.error('保存工单失败:', error);
      message.error('保存工单失败');
    }
  };

  // 更新工单状态
  const handleUpdateStatus = async (ticketId, status) => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        message.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugins/ai-customer-service/tickets/${ticketId}/status`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status })
      });

      if (response.ok) {
        message.success('状态更新成功');
        fetchTickets();
      } else {
        message.error('状态更新失败');
      }
    } catch (error) {
      console.error('更新状态失败:', error);
      message.error('更新状态失败');
    }
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    const statusMap = {
      'open': { color: 'red', text: '待处理' },
      'in_progress': { color: 'orange', text: '处理中' },
      'resolved': { color: 'green', text: '已解决' },
      'closed': { color: 'default', text: '已关闭' }
    };
    
    const statusInfo = statusMap[status] || { color: 'default', text: status };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  // 获取优先级标签
  const getPriorityTag = (priority) => {
    const priorityMap = {
      'high': { color: 'red', text: '高' },
      'medium': { color: 'orange', text: '中' },
      'low': { color: 'green', text: '低' }
    };
    
    const priorityInfo = priorityMap[priority] || { color: 'default', text: priority };
    return <Tag color={priorityInfo.color}>{priorityInfo.text}</Tag>;
  };

  // 获取分类标签
  const getCategoryTag = (category) => {
    const categoryMap = {
      'complaint': { color: 'red', text: '投诉' },
      'partnership': { color: 'blue', text: '合作' },
      'technical_support': { color: 'purple', text: '技术支持' },
      'sales_inquiry': { color: 'green', text: '销售咨询' }
    };
    
    const categoryInfo = categoryMap[category] || { color: 'default', text: category };
    return <Tag color={categoryInfo.color}>{categoryInfo.text}</Tag>;
  };

  // 检查是否逾期
  const isOverdue = (dueDate, status) => {
    if (status === 'resolved' || status === 'closed') return false;
    return dayjs().isAfter(dayjs(dueDate));
  };

  useEffect(() => {
    fetchTickets();
  }, []);

  useEffect(() => {
    filterTickets();
  }, [searchText, statusFilter, priorityFilter, dateRange, tickets]);

  const columns = [
    {
      title: '工单号',
      dataIndex: 'ticket_id',
      key: 'ticket_id',
      width: 100,
      render: (text) => <Text code>{text}</Text>
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      render: (text, record) => (
        <div>
          <div>{text}</div>
          {isOverdue(record.due_date, record.status) && (
            <Badge status="error" text="逾期" />
          )}
        </div>
      )
    },
    {
      title: '客户',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 100,
      render: (name) => (
        <Space>
          <UserOutlined />
          <span>{name}</span>
        </Space>
      )
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category) => getCategoryTag(category)
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => getStatusTag(status)
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority) => getPriorityTag(priority)
    },
    {
      title: '负责人',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 100
    },
    {
      title: '截止时间',
      dataIndex: 'due_date',
      key: 'due_date',
      width: 150,
      render: (time, record) => (
        <div>
          <ClockCircleOutlined style={{ 
            color: isOverdue(time, record.status) ? '#f5222d' : '#1890ff' 
          }} />
          <span style={{ marginLeft: 4 }}>
            {dayjs(time).format('MM-DD HH:mm')}
          </span>
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewTicket(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEditTicket(record)}
              size="small"
            />
          </Tooltip>
          {record.status !== 'resolved' && record.status !== 'closed' && (
            <Tooltip title="标记为已解决">
              <Button
                type="link"
                icon={<CheckOutlined />}
                onClick={() => handleUpdateStatus(record.id, 'resolved')}
                style={{ color: '#52c41a' }}
                size="small"
              />
            </Tooltip>
          )}
          {record.status !== 'closed' && (
            <Tooltip title="关闭工单">
              <Button
                type="link"
                icon={<CloseOutlined />}
                onClick={() => handleUpdateStatus(record.id, 'closed')}
                style={{ color: '#f5222d' }}
                size="small"
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  return (
    <div>
      <Card
        title="工单管理"
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateTicket}
            >
              创建工单
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchTickets}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        }
      >
        {/* 筛选条件 */}
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Search
              placeholder="搜索工单号、标题或客户"
              allowClear
              style={{ width: 250 }}
              onSearch={setSearchText}
            />
            <Select
              placeholder="状态"
              style={{ width: 120 }}
              value={statusFilter}
              onChange={setStatusFilter}
            >
              <Option value="all">全部状态</Option>
              <Option value="open">待处理</Option>
              <Option value="in_progress">处理中</Option>
              <Option value="resolved">已解决</Option>
              <Option value="closed">已关闭</Option>
            </Select>
            <Select
              placeholder="优先级"
              style={{ width: 120 }}
              value={priorityFilter}
              onChange={setPriorityFilter}
            >
              <Option value="all">全部优先级</Option>
              <Option value="high">高</Option>
              <Option value="medium">中</Option>
              <Option value="low">低</Option>
            </Select>
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              onChange={setDateRange}
            />
          </Space>
        </div>

        {/* 工单列表 */}
        <Table
          columns={columns}
          dataSource={filteredTickets}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredTickets.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 工单详情弹窗 */}
      <Modal
        title="工单详情"
        visible={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedTicket && (
          <div>
            <Card size="small" style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <Text strong style={{ fontSize: 16 }}>{selectedTicket.title}</Text>
                  <div style={{ marginTop: 8 }}>
                    <Space>
                      <Text code>{selectedTicket.ticket_id}</Text>
                      {getStatusTag(selectedTicket.status)}
                      {getPriorityTag(selectedTicket.priority)}
                      {getCategoryTag(selectedTicket.category)}
                    </Space>
                  </div>
                </div>
                <div style={{ textAlign: 'right' }}>
                  <div>负责人：{selectedTicket.assignee}</div>
                  <div>截止时间：{dayjs(selectedTicket.due_date).format('YYYY-MM-DD HH:mm')}</div>
                </div>
              </div>
            </Card>

            <Card title="工单描述" size="small" style={{ marginBottom: 16 }}>
              <Text>{selectedTicket.description}</Text>
            </Card>

            <Card title="客户信息" size="small" style={{ marginBottom: 16 }}>
              <Space direction="vertical">
                <div>客户姓名：{selectedTicket.customer_name}</div>
                <div>关联会话：{selectedTicket.session_id}</div>
                <div>创建时间：{selectedTicket.created_at}</div>
                <div>更新时间：{selectedTicket.updated_at}</div>
              </Space>
            </Card>

            {selectedTicket.resolution_notes && (
              <Card title="处理记录" size="small">
                <Text>{selectedTicket.resolution_notes}</Text>
              </Card>
            )}
          </div>
        )}
      </Modal>

      {/* 编辑工单弹窗 */}
      <Modal
        title="编辑工单"
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveTicket}
        >
          <Form.Item
            name="title"
            label="工单标题"
            rules={[{ required: true, message: '请输入工单标题' }]}
          >
            <Input placeholder="请输入工单标题" />
          </Form.Item>

          <Form.Item
            name="description"
            label="工单描述"
            rules={[{ required: true, message: '请输入工单描述' }]}
          >
            <TextArea rows={4} placeholder="请输入工单描述" />
          </Form.Item>

          <Form.Item
            name="category"
            label="工单分类"
            rules={[{ required: true, message: '请选择工单分类' }]}
          >
            <Select placeholder="请选择工单分类">
              <Option value="complaint">投诉</Option>
              <Option value="partnership">合作</Option>
              <Option value="technical_support">技术支持</Option>
              <Option value="sales_inquiry">销售咨询</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请选择优先级' }]}
          >
            <Select placeholder="请选择优先级">
              <Option value="high">高</Option>
              <Option value="medium">中</Option>
              <Option value="low">低</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="请选择状态">
              <Option value="open">待处理</Option>
              <Option value="in_progress">处理中</Option>
              <Option value="resolved">已解决</Option>
              <Option value="closed">已关闭</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="assignee"
            label="负责人"
          >
            <Input placeholder="请输入负责人" />
          </Form.Item>

          <Form.Item
            name="due_date"
            label="截止时间"
          >
            <DatePicker
              showTime
              placeholder="请选择截止时间"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="resolution_notes"
            label="处理记录"
          >
            <TextArea rows={3} placeholder="请输入处理记录" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 创建工单弹窗 */}
      <Modal
        title="创建工单"
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveTicket}
        >
          <Form.Item
            name="title"
            label="工单标题"
            rules={[{ required: true, message: '请输入工单标题' }]}
          >
            <Input placeholder="请输入工单标题" />
          </Form.Item>

          <Form.Item
            name="description"
            label="工单描述"
            rules={[{ required: true, message: '请输入工单描述' }]}
          >
            <TextArea rows={4} placeholder="请输入工单描述" />
          </Form.Item>

          <Form.Item
            name="category"
            label="工单分类"
            rules={[{ required: true, message: '请选择工单分类' }]}
          >
            <Select placeholder="请选择工单分类">
              <Option value="complaint">投诉</Option>
              <Option value="partnership">合作</Option>
              <Option value="technical_support">技术支持</Option>
              <Option value="sales_inquiry">销售咨询</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请选择优先级' }]}
            initialValue="medium"
          >
            <Select placeholder="请选择优先级">
              <Option value="high">高</Option>
              <Option value="medium">中</Option>
              <Option value="low">低</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
            initialValue="open"
          >
            <Select placeholder="请选择状态">
              <Option value="open">待处理</Option>
              <Option value="in_progress">处理中</Option>
              <Option value="resolved">已解决</Option>
              <Option value="closed">已关闭</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="assignee"
            label="负责人"
          >
            <Input placeholder="请输入负责人" />
          </Form.Item>

          <Form.Item
            name="due_date"
            label="截止时间"
          >
            <DatePicker
              showTime
              placeholder="请选择截止时间"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TicketManagement;
