import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  message,
  Typography,
  Alert,
  Descriptions,
  List,
  Badge,
  Tabs,
  Row,
  Col,
  Checkbox
} from 'antd';
import {
  BranchesOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  UserOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  BellOutlined,
  SettingOutlined,
  NotificationOutlined,
  MessageOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;
const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

const BusinessFlow = () => {
  const [loading, setLoading] = useState(false);
  const [flows, setFlows] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingFlow, setEditingFlow] = useState(null);
  const [notificationSettings, setNotificationSettings] = useState({
    websocket_enabled: true,
    email_enabled: true,
    sms_enabled: false,
    push_enabled: true,
    notification_channels: {
      complaint: ['websocket', 'email'],
      partnership: ['websocket', 'email'],
      franchise: ['websocket', 'email', 'push'],
      technical_support: ['websocket']
    }
  });
  const [taskSettings, setTaskSettings] = useState({
    auto_create_tasks: true,
    task_assignment_mode: 'round_robin', // round_robin, load_based, manual
    auto_escalation: true,
    escalation_timeout_hours: 2,
    task_priority_mapping: {
      high: 'urgent',
      medium: 'normal',
      low: 'low'
    }
  });
  const [form] = Form.useForm();
  const [notificationForm] = Form.useForm();
  const [taskForm] = Form.useForm();

  // 获取项目ID
  const getProjectId = () => {
    const projectId = localStorage.getItem('project_id');
    if (projectId && projectId !== 'null' && projectId !== 'undefined') {
      return projectId;
    }

    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    if (userData && userData.project_id) {
      localStorage.setItem('project_id', userData.project_id);
      return userData.project_id;
    }

    return null;
  };

  // 获取通知设置
  const fetchNotificationSettings = async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugins/ai-customer-service/notifications/settings`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setNotificationSettings(data);
        notificationForm.setFieldsValue(data);
      }
    } catch (error) {
      console.error('获取通知设置失败:', error);
    }
  };

  // 获取任务设置
  const fetchTaskSettings = async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugins/ai-customer-service/tasks/settings`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTaskSettings(data);
        taskForm.setFieldsValue(data);
      }
    } catch (error) {
      console.error('获取任务设置失败:', error);
    }
  };

  // 获取业务流转列表
  const fetchFlows = async () => {
    setLoading(true);
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        setLoading(false);
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugins/ai-customer-service/business-flows`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setFlows(data.flows || []);
      } else {
        // 如果API调用失败，使用模拟数据
        const mockFlows = [
        {
          id: '1',
          intent: 'complaint',
          intent_name: '客户投诉',
          flow_type: 'complaint_handling',
          target_roles: ['customer_service_manager', 'operations_manager'],
          priority: 'high',
          sla_hours: 2,
          is_enabled: true,
          trigger_count: 45,
          avg_resolution_time: 1.5,
          escalation_rules: {
            level_1: { roles: ['customer_service_manager'], timeout_hours: 1 },
            level_2: { roles: ['operations_manager'], timeout_hours: 2 }
          }
        },
        {
          id: '2',
          intent: 'partnership',
          intent_name: '合作咨询',
          flow_type: 'partnership_inquiry',
          target_roles: ['business_development', 'partnership_manager'],
          priority: 'medium',
          sla_hours: 24,
          is_enabled: true,
          trigger_count: 23,
          avg_resolution_time: 18.5,
          escalation_rules: {
            level_1: { roles: ['business_development'], timeout_hours: 8 },
            level_2: { roles: ['partnership_manager'], timeout_hours: 24 }
          }
        },
        {
          id: '3',
          intent: 'franchise',
          intent_name: '加盟咨询',
          flow_type: 'franchise_inquiry',
          target_roles: ['franchise_manager', 'regional_manager'],
          priority: 'medium',
          sla_hours: 12,
          is_enabled: true,
          trigger_count: 34,
          avg_resolution_time: 8.2,
          escalation_rules: {
            level_1: { roles: ['franchise_manager'], timeout_hours: 4 },
            level_2: { roles: ['regional_manager'], timeout_hours: 12 }
          }
        },
        {
          id: '4',
          intent: 'technical_support',
          intent_name: '技术支持',
          flow_type: 'technical_support',
          target_roles: ['technical_support', 'senior_engineer'],
          priority: 'medium',
          sla_hours: 8,
          is_enabled: false,
          trigger_count: 67,
          avg_resolution_time: 4.8,
          escalation_rules: {
            level_1: { roles: ['technical_support'], timeout_hours: 2 },
            level_2: { roles: ['senior_engineer'], timeout_hours: 8 }
          }
        }
        ];

        setFlows(mockFlows);
      }
    } catch (error) {
      console.error('获取业务流转列表失败:', error);
      message.error('获取业务流转列表失败');
      setFlows([]);
    } finally {
      setLoading(false);
    }
  };

  // 保存业务流转
  const handleSaveFlow = async (values) => {
    try {
      setLoading(true);
      
      if (editingFlow) {
        console.log('编辑业务流转:', editingFlow.id, values);
        message.success('业务流转更新成功');
      } else {
        console.log('添加业务流转:', values);
        message.success('业务流转添加成功');
      }
      
      setModalVisible(false);
      setEditingFlow(null);
      form.resetFields();
      fetchFlows();
    } catch (error) {
      console.error('保存业务流转失败:', error);
      message.error('保存业务流转失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除业务流转
  const handleDeleteFlow = (flow) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除业务流转 "${flow.intent_name}" 吗？`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          console.log('删除业务流转:', flow.id);
          message.success('业务流转删除成功');
          fetchFlows();
        } catch (error) {
          console.error('删除业务流转失败:', error);
          message.error('删除业务流转失败');
        }
      }
    });
  };

  // 切换流转状态
  const handleToggleFlow = async (flow, enabled) => {
    try {
      console.log('切换流转状态:', flow.id, enabled);
      message.success(`业务流转已${enabled ? '启用' : '禁用'}`);
      fetchFlows();
    } catch (error) {
      console.error('切换流转状态失败:', error);
      message.error('切换流转状态失败');
    }
  };

  // 获取优先级标签
  const getPriorityTag = (priority) => {
    const priorityMap = {
      'high': { color: 'red', text: '高' },
      'medium': { color: 'orange', text: '中' },
      'low': { color: 'green', text: '低' }
    };
    
    const priorityInfo = priorityMap[priority] || { color: 'default', text: priority };
    return <Tag color={priorityInfo.color}>{priorityInfo.text}</Tag>;
  };

  // 获取角色显示
  const getRolesDisplay = (roles) => {
    const roleMap = {
      'customer_service_manager': '客服经理',
      'operations_manager': '运营经理',
      'business_development': '商务拓展',
      'partnership_manager': '合作经理',
      'franchise_manager': '加盟经理',
      'regional_manager': '区域经理',
      'technical_support': '技术支持',
      'senior_engineer': '高级工程师'
    };
    
    return roles.map(role => (
      <Tag key={role} color="blue">
        {roleMap[role] || role}
      </Tag>
    ));
  };

  useEffect(() => {
    fetchFlows();
  }, []);

  const columns = [
    {
      title: '意图类型',
      key: 'intent',
      width: 120,
      render: (_, record) => (
        <div>
          <div>{record.intent_name}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.intent}
          </Text>
        </div>
      )
    },
    {
      title: '目标角色',
      dataIndex: 'target_roles',
      key: 'target_roles',
      width: 200,
      render: (roles) => (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
          {getRolesDisplay(roles)}
        </div>
      )
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority) => getPriorityTag(priority)
    },
    {
      title: 'SLA',
      dataIndex: 'sla_hours',
      key: 'sla_hours',
      width: 80,
      render: (hours) => `${hours}小时`
    },
    {
      title: '触发次数',
      dataIndex: 'trigger_count',
      key: 'trigger_count',
      width: 80,
      render: (count) => <Badge count={count} showZero />
    },
    {
      title: '平均处理时间',
      dataIndex: 'avg_resolution_time',
      key: 'avg_resolution_time',
      width: 120,
      render: (time) => `${time}小时`
    },
    {
      title: '状态',
      key: 'status',
      width: 80,
      render: (_, record) => (
        <Switch
          checked={record.is_enabled}
          onChange={(checked) => handleToggleFlow(record, checked)}
          size="small"
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingFlow(record);
              form.setFieldsValue({
                intent: record.intent,
                intent_name: record.intent_name,
                target_roles: record.target_roles,
                priority: record.priority,
                sla_hours: record.sla_hours
              });
              setModalVisible(true);
            }}
          />
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteFlow(record)}
          />
        </Space>
      )
    }
  ];

  return (
    <div>
      <Card
        title="业务流转管理"
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingFlow(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              添加流转
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchFlows}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <Alert
          message="业务流转功能"
          description={
            <div>
              <Paragraph>
                基于意图识别，自动将客户问题转入相应业务流程，并通知相关人员创建任务。
                支持多级升级机制和SLA管理。
              </Paragraph>
              <ul>
                <li><strong>投诉处理</strong>：自动转给客服经理和运营经理</li>
                <li><strong>合作咨询</strong>：转给商务拓展和合作经理</li>
                <li><strong>加盟咨询</strong>：转给加盟经理和区域经理</li>
                <li><strong>技术支持</strong>：转给技术支持和高级工程师</li>
              </ul>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Table
          columns={columns}
          dataSource={flows}
          rowKey="id"
          loading={loading}
          pagination={false}
        />
      </Card>

      {/* 添加/编辑流转弹窗 */}
      <Modal
        title={editingFlow ? '编辑业务流转' : '添加业务流转'}
        visible={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingFlow(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        confirmLoading={loading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveFlow}
        >
          <Form.Item
            name="intent"
            label="意图标识"
            rules={[{ required: true, message: '请输入意图标识' }]}
          >
            <Input placeholder="输入意图标识，如：complaint" />
          </Form.Item>

          <Form.Item
            name="intent_name"
            label="意图名称"
            rules={[{ required: true, message: '请输入意图名称' }]}
          >
            <Input placeholder="输入意图名称，如：客户投诉" />
          </Form.Item>

          <Form.Item
            name="target_roles"
            label="目标角色"
            rules={[{ required: true, message: '请选择目标角色' }]}
          >
            <Select mode="multiple" placeholder="选择目标角色">
              <Option value="customer_service_manager">客服经理</Option>
              <Option value="operations_manager">运营经理</Option>
              <Option value="business_development">商务拓展</Option>
              <Option value="partnership_manager">合作经理</Option>
              <Option value="franchise_manager">加盟经理</Option>
              <Option value="regional_manager">区域经理</Option>
              <Option value="technical_support">技术支持</Option>
              <Option value="senior_engineer">高级工程师</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请选择优先级' }]}
          >
            <Select placeholder="选择优先级">
              <Option value="high">高</Option>
              <Option value="medium">中</Option>
              <Option value="low">低</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="sla_hours"
            label="SLA时限（小时）"
            rules={[{ required: true, message: '请输入SLA时限' }]}
          >
            <InputNumber
              min={1}
              max={168}
              placeholder="输入处理时限"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default BusinessFlow;
