import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Progress,
  Table,
  Tag,
  Alert,
  Typography,
  Row,
  Col,
  Statistic,
  List,
  Badge,
  Modal,
  message,
  Tabs,
  Form,
  Switch,
  InputNumber,
  Select,
  Slider
} from 'antd';
import {
  BulbOutlined,
  RocketOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  TrophyOutlined,
  RiseOutlined,
  ReloadOutlined
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

const LearningOptimization = () => {
  const [loading, setLoading] = useState(false);
  const [analysisData, setAnalysisData] = useState(null);
  const [optimizationHistory, setOptimizationHistory] = useState([]);
  const [learningConfig, setLearningConfig] = useState({
    auto_optimization_enabled: true,
    optimization_frequency: 'daily',
    quality_threshold: 0.7,
    satisfaction_threshold: 4.0,
    response_time_threshold: 3.0,
    learning_rate: 0.1,
    min_samples_for_optimization: 100,
    enable_intent_learning: true,
    enable_response_optimization: true,
    enable_performance_tuning: true
  });
  const [configForm] = Form.useForm();

  // 获取项目ID
  const getProjectId = () => {
    const projectId = localStorage.getItem('project_id');
    if (projectId && projectId !== 'null' && projectId !== 'undefined') {
      return projectId;
    }

    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    if (userData && userData.project_id) {
      localStorage.setItem('project_id', userData.project_id);
      return userData.project_id;
    }

    return null;
  };

  // 获取学习配置
  const fetchLearningConfig = async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugins/ai-customer-service/learning/config`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setLearningConfig(data);
        configForm.setFieldsValue(data);
      }
    } catch (error) {
      console.error('获取学习配置失败:', error);
    }
  };

  // 保存学习配置
  const handleSaveConfig = async (values) => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        message.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugins/ai-customer-service/learning/config`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(values)
      });

      if (response.ok) {
        setLearningConfig(values);
        message.success('学习配置保存成功');
      } else {
        message.error('保存学习配置失败');
      }
    } catch (error) {
      console.error('保存学习配置失败:', error);
      message.error('保存学习配置失败');
    }
  };

  // 获取学习分析数据
  const fetchAnalysisData = async () => {
    setLoading(true);
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        setLoading(false);
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugins/ai-customer-service/learning/analysis`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAnalysisData(data);
      } else {
        // 如果API调用失败，使用模拟数据
        const mockData = {
        overall_score: 0.78,
        conversation_quality: {
          avg_quality: 0.75,
          total_messages: 8945,
          quality_distribution: {
            excellent: 2156,
            good: 3245,
            average: 2987,
            poor: 557
          }
        },
        intent_accuracy: {
          accuracy_score: 0.82,
          total_sessions: 1256,
          successful_sessions: 1030
        },
        response_times: {
          avg_response_time: 2.3,
          p95_response_time: 4.8,
          slow_responses_count: 89
        },
        customer_satisfaction: {
          avg_satisfaction: 4.2,
          total_rated: 856,
          distribution: {
            excellent: 456,
            good: 234,
            average: 123,
            poor: 43
          }
        },
        optimization_suggestions: [
          {
            type: 'quality_improvement',
            priority: 'high',
            title: '提升回复质量',
            description: 'AI回复质量偏低，建议优化提示词和训练数据',
            actions: [
              '分析低质量回复模式',
              '优化AI提示词',
              '增加训练样本',
              '改进知识库内容'
            ]
          },
          {
            type: 'performance_optimization',
            priority: 'medium',
            title: '优化响应速度',
            description: '响应时间过长，需要优化系统性能',
            actions: [
              '优化AI模型推理速度',
              '增加缓存机制',
              '优化知识库检索',
              '考虑使用更快的模型'
            ]
          },
          {
            type: 'satisfaction_improvement',
            priority: 'high',
            title: '提升客户满意度',
            description: '客户满意度有待提升',
            actions: [
              '分析低满意度对话',
              '改进回复的个性化程度',
              '增强情感理解能力',
              '优化业务流转效率'
            ]
          }
        ]
      };
      
        setAnalysisData(mockData);
      }
    } catch (error) {
      console.error('获取分析数据失败:', error);
      message.error('获取分析数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取优化历史
  const fetchOptimizationHistory = async () => {
    try {
      const mockHistory = [
        {
          id: '1',
          date: '2025-01-08',
          type: 'automatic',
          improvements: [
            '优化了产品咨询的回复模板',
            '改进了意图识别准确性',
            '调整了响应时间阈值'
          ],
          performance_gain: 0.08,
          status: 'completed'
        },
        {
          id: '2',
          date: '2025-01-06',
          type: 'manual',
          improvements: [
            '更新了知识库内容',
            '添加了新的FAQ条目'
          ],
          performance_gain: 0.05,
          status: 'completed'
        },
        {
          id: '3',
          date: '2025-01-04',
          type: 'automatic',
          improvements: [
            '优化了缓存策略',
            '改进了错误处理机制'
          ],
          performance_gain: 0.03,
          status: 'completed'
        }
      ];
      
      setOptimizationHistory(mockHistory);
    } catch (error) {
      console.error('获取优化历史失败:', error);
    }
  };

  // 应用优化建议
  const handleApplyOptimization = async (suggestion) => {
    try {
      setLoading(true);
      console.log('应用优化建议:', suggestion);
      
      // 模拟应用过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      message.success('优化建议已应用');
      fetchAnalysisData();
      fetchOptimizationHistory();
    } catch (error) {
      console.error('应用优化失败:', error);
      message.error('应用优化失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取优先级标签
  const getPriorityTag = (priority) => {
    const priorityMap = {
      'high': { color: 'red', text: '高' },
      'medium': { color: 'orange', text: '中' },
      'low': { color: 'green', text: '低' }
    };
    
    const priorityInfo = priorityMap[priority] || { color: 'default', text: priority };
    return <Tag color={priorityInfo.color}>{priorityInfo.text}</Tag>;
  };

  // 获取类型标签
  const getTypeTag = (type) => {
    const typeMap = {
      'automatic': { color: 'blue', text: '自动' },
      'manual': { color: 'purple', text: '手动' }
    };
    
    const typeInfo = typeMap[type] || { color: 'default', text: type };
    return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>;
  };

  useEffect(() => {
    fetchAnalysisData();
    fetchOptimizationHistory();
    fetchLearningConfig();
  }, []);

  if (!analysisData) {
    return <Card loading={loading}>加载中...</Card>;
  }

  return (
    <div>
      <Card title="智能学习优化">
        <Alert
          message="智能学习功能"
          description="基于对话数据自动学习和优化AI回复质量、意图识别准确性，实现系统的自我完善和持续改进。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Tabs defaultActiveKey="1">
          <TabPane tab="学习分析" key="1">
            {/* 综合评分 */}
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={24}>
                <Card size="small">
                  <div style={{ textAlign: 'center' }}>
                    <Title level={3}>综合评分</Title>
                    <Progress
                      type="circle"
                      percent={Math.round(analysisData.overall_score * 100)}
                      format={percent => `${percent}分`}
                      strokeColor="#52c41a"
                      size={120}
                    />
                    <div style={{ marginTop: 16 }}>
                      <Text type="secondary">
                        基于对话质量、意图准确性、客户满意度等指标综合评估
                      </Text>
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>

            {/* 详细指标 */}
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col xs={24} sm={12} md={6}>
                <Card size="small">
                  <Statistic
                    title="对话质量"
                    value={Math.round(analysisData.conversation_quality.avg_quality * 100)}
                    suffix="分"
                    prefix={<BulbOutlined />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                  <Progress 
                    percent={Math.round(analysisData.conversation_quality.avg_quality * 100)} 
                    size="small" 
                    showInfo={false}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card size="small">
                  <Statistic
                    title="意图准确性"
                    value={Math.round(analysisData.intent_accuracy.accuracy_score * 100)}
                    suffix="分"
                    prefix={<TrophyOutlined />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                  <Progress 
                    percent={Math.round(analysisData.intent_accuracy.accuracy_score * 100)} 
                    size="small" 
                    showInfo={false}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card size="small">
                  <Statistic
                    title="响应速度"
                    value={analysisData.response_times.avg_response_time}
                    precision={1}
                    suffix="秒"
                    prefix={<RocketOutlined />}
                    valueStyle={{ color: '#722ed1' }}
                  />
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    P95: {analysisData.response_times.p95_response_time}秒
                  </Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card size="small">
                  <Statistic
                    title="客户满意度"
                    value={analysisData.customer_satisfaction.avg_satisfaction}
                    precision={1}
                    suffix="/ 5.0"
                    prefix={<RiseOutlined />}
                    valueStyle={{ color: '#f5222d' }}
                  />
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {analysisData.customer_satisfaction.total_rated} 次评价
                  </Text>
                </Card>
              </Col>
            </Row>

            {/* 优化建议 */}
            <Card title="优化建议" size="small">
              <List
                dataSource={analysisData.optimization_suggestions}
                renderItem={suggestion => (
                  <List.Item
                    actions={[
                      <Button
                        type="primary"
                        size="small"
                        onClick={() => handleApplyOptimization(suggestion)}
                        loading={loading}
                      >
                        应用优化
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <Badge 
                          status={suggestion.priority === 'high' ? 'error' : 'warning'} 
                        />
                      }
                      title={
                        <Space>
                          <span>{suggestion.title}</span>
                          {getPriorityTag(suggestion.priority)}
                        </Space>
                      }
                      description={
                        <div>
                          <Paragraph style={{ marginBottom: 8 }}>
                            {suggestion.description}
                          </Paragraph>
                          <div>
                            <Text strong>改进措施：</Text>
                            <ul style={{ marginTop: 4, marginBottom: 0 }}>
                              {suggestion.actions.map((action, index) => (
                                <li key={index}>{action}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          </TabPane>

          <TabPane tab="优化历史" key="2">
            <Card 
              title="优化历史记录"
              extra={
                <Button
                  icon={<ReloadOutlined />}
                  onClick={fetchOptimizationHistory}
                  size="small"
                >
                  刷新
                </Button>
              }
            >
              <List
                dataSource={optimizationHistory}
                renderItem={record => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Badge 
                          status={record.status === 'completed' ? 'success' : 'processing'} 
                        />
                      }
                      title={
                        <Space>
                          <span>{record.date}</span>
                          {getTypeTag(record.type)}
                          <Tag color="green">+{(record.performance_gain * 100).toFixed(1)}%</Tag>
                        </Space>
                      }
                      description={
                        <div>
                          <Text strong>优化内容：</Text>
                          <ul style={{ marginTop: 4, marginBottom: 0 }}>
                            {record.improvements.map((improvement, index) => (
                              <li key={index}>{improvement}</li>
                            ))}
                          </ul>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          </TabPane>

          <TabPane tab="学习配置" key="3">
            <Card title="学习配置">
              <Form
                form={configForm}
                layout="vertical"
                onFinish={handleSaveConfig}
                initialValues={learningConfig}
              >
                <Row gutter={16}>
                  <Col span={12}>
                    <Card title="基础配置" size="small" style={{ marginBottom: 16 }}>
                      <Form.Item
                        name="auto_optimization_enabled"
                        label="启用自动优化"
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>

                      <Form.Item
                        name="optimization_frequency"
                        label="优化频率"
                      >
                        <Select>
                          <Option value="hourly">每小时</Option>
                          <Option value="daily">每天</Option>
                          <Option value="weekly">每周</Option>
                          <Option value="monthly">每月</Option>
                        </Select>
                      </Form.Item>

                      <Form.Item
                        name="min_samples_for_optimization"
                        label="最小样本数"
                        help="触发优化所需的最小对话样本数"
                      >
                        <InputNumber min={10} max={1000} style={{ width: '100%' }} />
                      </Form.Item>
                    </Card>
                  </Col>

                  <Col span={12}>
                    <Card title="阈值配置" size="small" style={{ marginBottom: 16 }}>
                      <Form.Item
                        name="quality_threshold"
                        label="质量阈值"
                        help="低于此阈值将触发质量优化"
                      >
                        <Slider
                          min={0}
                          max={1}
                          step={0.1}
                          marks={{
                            0: '0',
                            0.5: '0.5',
                            1: '1'
                          }}
                        />
                      </Form.Item>

                      <Form.Item
                        name="satisfaction_threshold"
                        label="满意度阈值"
                        help="低于此阈值将触发满意度优化"
                      >
                        <Slider
                          min={1}
                          max={5}
                          step={0.1}
                          marks={{
                            1: '1',
                            3: '3',
                            5: '5'
                          }}
                        />
                      </Form.Item>

                      <Form.Item
                        name="response_time_threshold"
                        label="响应时间阈值（秒）"
                        help="超过此时间将触发性能优化"
                      >
                        <Slider
                          min={1}
                          max={10}
                          step={0.5}
                          marks={{
                            1: '1s',
                            5: '5s',
                            10: '10s'
                          }}
                        />
                      </Form.Item>
                    </Card>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Card title="学习参数" size="small" style={{ marginBottom: 16 }}>
                      <Form.Item
                        name="learning_rate"
                        label="学习率"
                        help="控制模型学习的速度"
                      >
                        <Slider
                          min={0.01}
                          max={0.5}
                          step={0.01}
                          marks={{
                            0.01: '0.01',
                            0.1: '0.1',
                            0.5: '0.5'
                          }}
                        />
                      </Form.Item>
                    </Card>
                  </Col>

                  <Col span={12}>
                    <Card title="优化模块" size="small" style={{ marginBottom: 16 }}>
                      <Form.Item
                        name="enable_intent_learning"
                        label="启用意图学习"
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>

                      <Form.Item
                        name="enable_response_optimization"
                        label="启用回复优化"
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>

                      <Form.Item
                        name="enable_performance_tuning"
                        label="启用性能调优"
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>
                    </Card>
                  </Col>
                </Row>

                <Form.Item>
                  <Space>
                    <Button type="primary" htmlType="submit">
                      保存配置
                    </Button>
                    <Button onClick={() => configForm.resetFields()}>
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default LearningOptimization;
