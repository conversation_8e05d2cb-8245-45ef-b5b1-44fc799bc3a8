import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Progress,
  Table,
  Tag,
  Alert,
  Typography,
  Row,
  Col,
  Statistic,
  List,
  Badge,
  Modal,
  message,
  Tabs
} from 'antd';
import {
  BulbOutlined,
  RocketOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  TrophyOutlined,
  RiseOutlined,
  ReloadOutlined
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

const LearningOptimization = () => {
  const [loading, setLoading] = useState(false);
  const [analysisData, setAnalysisData] = useState(null);
  const [optimizationHistory, setOptimizationHistory] = useState([]);

  // 获取学习分析数据
  const fetchAnalysisData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      const mockData = {
        overall_score: 0.78,
        conversation_quality: {
          avg_quality: 0.75,
          total_messages: 8945,
          quality_distribution: {
            excellent: 2156,
            good: 3245,
            average: 2987,
            poor: 557
          }
        },
        intent_accuracy: {
          accuracy_score: 0.82,
          total_sessions: 1256,
          successful_sessions: 1030
        },
        response_times: {
          avg_response_time: 2.3,
          p95_response_time: 4.8,
          slow_responses_count: 89
        },
        customer_satisfaction: {
          avg_satisfaction: 4.2,
          total_rated: 856,
          distribution: {
            excellent: 456,
            good: 234,
            average: 123,
            poor: 43
          }
        },
        optimization_suggestions: [
          {
            type: 'quality_improvement',
            priority: 'high',
            title: '提升回复质量',
            description: 'AI回复质量偏低，建议优化提示词和训练数据',
            actions: [
              '分析低质量回复模式',
              '优化AI提示词',
              '增加训练样本',
              '改进知识库内容'
            ]
          },
          {
            type: 'performance_optimization',
            priority: 'medium',
            title: '优化响应速度',
            description: '响应时间过长，需要优化系统性能',
            actions: [
              '优化AI模型推理速度',
              '增加缓存机制',
              '优化知识库检索',
              '考虑使用更快的模型'
            ]
          },
          {
            type: 'satisfaction_improvement',
            priority: 'high',
            title: '提升客户满意度',
            description: '客户满意度有待提升',
            actions: [
              '分析低满意度对话',
              '改进回复的个性化程度',
              '增强情感理解能力',
              '优化业务流转效率'
            ]
          }
        ]
      };
      
      setAnalysisData(mockData);
    } catch (error) {
      console.error('获取分析数据失败:', error);
      message.error('获取分析数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取优化历史
  const fetchOptimizationHistory = async () => {
    try {
      const mockHistory = [
        {
          id: '1',
          date: '2025-01-08',
          type: 'automatic',
          improvements: [
            '优化了产品咨询的回复模板',
            '改进了意图识别准确性',
            '调整了响应时间阈值'
          ],
          performance_gain: 0.08,
          status: 'completed'
        },
        {
          id: '2',
          date: '2025-01-06',
          type: 'manual',
          improvements: [
            '更新了知识库内容',
            '添加了新的FAQ条目'
          ],
          performance_gain: 0.05,
          status: 'completed'
        },
        {
          id: '3',
          date: '2025-01-04',
          type: 'automatic',
          improvements: [
            '优化了缓存策略',
            '改进了错误处理机制'
          ],
          performance_gain: 0.03,
          status: 'completed'
        }
      ];
      
      setOptimizationHistory(mockHistory);
    } catch (error) {
      console.error('获取优化历史失败:', error);
    }
  };

  // 应用优化建议
  const handleApplyOptimization = async (suggestion) => {
    try {
      setLoading(true);
      console.log('应用优化建议:', suggestion);
      
      // 模拟应用过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      message.success('优化建议已应用');
      fetchAnalysisData();
      fetchOptimizationHistory();
    } catch (error) {
      console.error('应用优化失败:', error);
      message.error('应用优化失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取优先级标签
  const getPriorityTag = (priority) => {
    const priorityMap = {
      'high': { color: 'red', text: '高' },
      'medium': { color: 'orange', text: '中' },
      'low': { color: 'green', text: '低' }
    };
    
    const priorityInfo = priorityMap[priority] || { color: 'default', text: priority };
    return <Tag color={priorityInfo.color}>{priorityInfo.text}</Tag>;
  };

  // 获取类型标签
  const getTypeTag = (type) => {
    const typeMap = {
      'automatic': { color: 'blue', text: '自动' },
      'manual': { color: 'purple', text: '手动' }
    };
    
    const typeInfo = typeMap[type] || { color: 'default', text: type };
    return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>;
  };

  useEffect(() => {
    fetchAnalysisData();
    fetchOptimizationHistory();
  }, []);

  if (!analysisData) {
    return <Card loading={loading}>加载中...</Card>;
  }

  return (
    <div>
      <Card title="智能学习优化">
        <Alert
          message="智能学习功能"
          description="基于对话数据自动学习和优化AI回复质量、意图识别准确性，实现系统的自我完善和持续改进。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Tabs defaultActiveKey="1">
          <TabPane tab="学习分析" key="1">
            {/* 综合评分 */}
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={24}>
                <Card size="small">
                  <div style={{ textAlign: 'center' }}>
                    <Title level={3}>综合评分</Title>
                    <Progress
                      type="circle"
                      percent={Math.round(analysisData.overall_score * 100)}
                      format={percent => `${percent}分`}
                      strokeColor="#52c41a"
                      size={120}
                    />
                    <div style={{ marginTop: 16 }}>
                      <Text type="secondary">
                        基于对话质量、意图准确性、客户满意度等指标综合评估
                      </Text>
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>

            {/* 详细指标 */}
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col xs={24} sm={12} md={6}>
                <Card size="small">
                  <Statistic
                    title="对话质量"
                    value={Math.round(analysisData.conversation_quality.avg_quality * 100)}
                    suffix="分"
                    prefix={<BulbOutlined />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                  <Progress 
                    percent={Math.round(analysisData.conversation_quality.avg_quality * 100)} 
                    size="small" 
                    showInfo={false}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card size="small">
                  <Statistic
                    title="意图准确性"
                    value={Math.round(analysisData.intent_accuracy.accuracy_score * 100)}
                    suffix="分"
                    prefix={<TrophyOutlined />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                  <Progress 
                    percent={Math.round(analysisData.intent_accuracy.accuracy_score * 100)} 
                    size="small" 
                    showInfo={false}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card size="small">
                  <Statistic
                    title="响应速度"
                    value={analysisData.response_times.avg_response_time}
                    precision={1}
                    suffix="秒"
                    prefix={<RocketOutlined />}
                    valueStyle={{ color: '#722ed1' }}
                  />
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    P95: {analysisData.response_times.p95_response_time}秒
                  </Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card size="small">
                  <Statistic
                    title="客户满意度"
                    value={analysisData.customer_satisfaction.avg_satisfaction}
                    precision={1}
                    suffix="/ 5.0"
                    prefix={<RiseOutlined />}
                    valueStyle={{ color: '#f5222d' }}
                  />
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {analysisData.customer_satisfaction.total_rated} 次评价
                  </Text>
                </Card>
              </Col>
            </Row>

            {/* 优化建议 */}
            <Card title="优化建议" size="small">
              <List
                dataSource={analysisData.optimization_suggestions}
                renderItem={suggestion => (
                  <List.Item
                    actions={[
                      <Button
                        type="primary"
                        size="small"
                        onClick={() => handleApplyOptimization(suggestion)}
                        loading={loading}
                      >
                        应用优化
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <Badge 
                          status={suggestion.priority === 'high' ? 'error' : 'warning'} 
                        />
                      }
                      title={
                        <Space>
                          <span>{suggestion.title}</span>
                          {getPriorityTag(suggestion.priority)}
                        </Space>
                      }
                      description={
                        <div>
                          <Paragraph style={{ marginBottom: 8 }}>
                            {suggestion.description}
                          </Paragraph>
                          <div>
                            <Text strong>改进措施：</Text>
                            <ul style={{ marginTop: 4, marginBottom: 0 }}>
                              {suggestion.actions.map((action, index) => (
                                <li key={index}>{action}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          </TabPane>

          <TabPane tab="优化历史" key="2">
            <Card 
              title="优化历史记录"
              extra={
                <Button
                  icon={<ReloadOutlined />}
                  onClick={fetchOptimizationHistory}
                  size="small"
                >
                  刷新
                </Button>
              }
            >
              <List
                dataSource={optimizationHistory}
                renderItem={record => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Badge 
                          status={record.status === 'completed' ? 'success' : 'processing'} 
                        />
                      }
                      title={
                        <Space>
                          <span>{record.date}</span>
                          {getTypeTag(record.type)}
                          <Tag color="green">+{(record.performance_gain * 100).toFixed(1)}%</Tag>
                        </Space>
                      }
                      description={
                        <div>
                          <Text strong>优化内容：</Text>
                          <ul style={{ marginTop: 4, marginBottom: 0 }}>
                            {record.improvements.map((improvement, index) => (
                              <li key={index}>{improvement}</li>
                            ))}
                          </ul>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          </TabPane>

          <TabPane tab="学习配置" key="3">
            <Card title="学习配置">
              <Alert
                message="配置功能开发中"
                description="学习参数配置、优化策略设置等功能正在开发中。"
                type="warning"
                showIcon
              />
            </Card>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default LearningOptimization;
