import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  List,
  Button,
  Space,
  Input,
  Tag,
  Badge,
  Tooltip,
  message,
  Modal,
  Typography,
  Divider,
  Select,
  Row,
  Col,
  Spin,
  Empty
} from 'antd';
import {
  AppstoreOutlined,
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  CloseCircleOutlined,
  RobotOutlined,
  ApiOutlined,
  ToolOutlined,
  ShopOutlined,
  DollarOutlined,
  PieChartOutlined,
  MessageOutlined,
  ShoppingCartOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { getPluginMarket, getPluginDetail, installPlugin, uninstallPlugin } from '../../../../services/pluginService';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Search } = Input;

/**
 * 统一插件市场组件
 *
 * 展示所有类型的插件，包括市场插件、AI插件和系统插件
 */
const UnifiedPluginMarket = () => {
  const [loading, setLoading] = useState(false);
  const [plugins, setPlugins] = useState([]);
  const [filteredPlugins, setFilteredPlugins] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [activeCategory, setActiveCategory] = useState('all');
  const [activeType, setActiveType] = useState('all');
  const [categories, setCategories] = useState([]);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedPlugin, setSelectedPlugin] = useState(null);

  const navigate = useNavigate();

  // 获取插件列表
  const fetchPlugins = async () => {
    setLoading(true);
    try {
      const response = await getPluginMarket();

      if (response.success) {
        const pluginData = response.data.plugins || [];
        setPlugins(pluginData);
        filterPlugins(pluginData, activeTab, activeCategory, activeType, searchText);

        // 提取分类
        const uniqueCategories = [...new Set(pluginData.map(plugin => plugin.category))];
        setCategories(uniqueCategories.map(category => ({
          value: category,
          label: getCategoryLabel(category)
        })));
      } else {
        message.error('获取插件列表失败');
      }
    } catch (error) {
      console.error('获取插件列表失败:', error);
      message.error('获取插件列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 过滤插件
  const filterPlugins = (plugins, tab, category, type, search) => {
    let filtered = [...plugins];

    // 按标签页过滤
    if (tab === 'installed') {
      filtered = filtered.filter(plugin => plugin.is_installed);
    } else if (tab === 'marketplace') {
      filtered = filtered.filter(plugin => plugin.type === 'marketplace');
    } else if (tab === 'ai') {
      filtered = filtered.filter(plugin => plugin.type === 'ai');
    } else if (tab === 'system') {
      filtered = filtered.filter(plugin => plugin.type === 'system');
    }

    // 按分类过滤
    if (category !== 'all') {
      filtered = filtered.filter(plugin => plugin.category === category);
    }

    // 按类型过滤
    if (type !== 'all') {
      filtered = filtered.filter(plugin => plugin.type === type);
    }

    // 按搜索文本过滤
    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(plugin =>
        plugin.name.toLowerCase().includes(searchLower) ||
        plugin.description.toLowerCase().includes(searchLower)
      );
    }

    setFilteredPlugins(filtered);
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value);
    filterPlugins(plugins, activeTab, activeCategory, activeType, value);
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
    filterPlugins(plugins, key, activeCategory, activeType, searchText);
  };

  // 处理分类切换
  const handleCategoryChange = (category) => {
    setActiveCategory(category);
    filterPlugins(plugins, activeTab, category, activeType, searchText);
  };

  // 处理类型切换
  const handleTypeChange = (type) => {
    setActiveType(type);
    filterPlugins(plugins, activeTab, activeCategory, type, searchText);
  };

  // 处理查看插件详情
  const handleViewPlugin = (plugin) => {
    setSelectedPlugin(plugin);
    setDetailModalVisible(true);
  };

  // 处理安装插件
  const handleInstallPlugin = async (plugin) => {
    try {
      setLoading(true);
      const response = await installPlugin(plugin.id);

      if (response.success) {
        message.success('插件安装成功');
        fetchPlugins();
      } else {
        message.error('插件安装失败');
      }
    } catch (error) {
      console.error('安装插件失败:', error);
      message.error('安装插件失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理卸载插件
  const handleUninstallPlugin = async (plugin) => {
    try {
      Modal.confirm({
        title: '确认卸载',
        content: `确定要卸载插件 "${plugin.name}" 吗？卸载后插件的所有数据将被删除。`,
        okText: '确认卸载',
        okType: 'danger',
        cancelText: '取消',
        onOk: async () => {
          setLoading(true);
          const response = await uninstallPlugin(plugin.id);

          if (response.success) {
            message.success('插件卸载成功');
            fetchPlugins();
          } else {
            message.error('插件卸载失败');
          }
          setLoading(false);
        }
      });
    } catch (error) {
      console.error('卸载插件失败:', error);
      message.error('卸载插件失败');
    }
  };

  // 处理配置插件
  const handleConfigPlugin = (plugin) => {
    // 根据插件类型和名称构建路由
    const pluginName = plugin.name.toLowerCase().replace(/\s+/g, '_');

    // 特殊处理智能助手集成平台插件
    if (plugin.code === 'sapi') {
      navigate('/project/plugins/sapi');
      return;
    }

    // 特殊处理钉钉智能机器人插件
    if (plugin.code === 'dingtalk_robot' || plugin.code === 'dingtalk') {
      navigate('/project/plugins/dingtalk');
      return;
    }

    // 特殊处理AI智能体客服插件
    if (plugin.code === 'ai_customer_service') {
      navigate('/project/plugins/ai_customer_service');
      return;
    }

    if (plugin.type === 'marketplace') {
      navigate(`/project/plugin/${pluginName}`);
    } else if (plugin.type === 'ai') {
      navigate(`/project/ai/plugin/${pluginName}`);
    } else if (plugin.type === 'system') {
      navigate(`/system/plugin/${pluginName}`);
    }
  };

  // 获取分类标签
  const getCategoryLabel = (category) => {
    const categoryMap = {
      'marketing': '营销',
      'ai': '人工智能',
      'ai_agent': 'AI智能体',
      'communication': '通信协作',
      'customer_service': '客户服务',
      'message': '消息通知',
      'finance': '财务',
      'analytics': '数据分析',
      'integration': '系统集成',
      'utility': '实用工具'
    };

    return categoryMap[category] || category;
  };

  // 获取类型图标
  const getTypeIcon = (type) => {
    if (type === 'marketplace') {
      return <ShopOutlined />;
    } else if (type === 'ai') {
      return <RobotOutlined />;
    } else if (type === 'system') {
      return <SettingOutlined />;
    }
    return <AppstoreOutlined />;
  };

  // 获取分类图标
  const getCategoryIcon = (category) => {
    const iconMap = {
      'marketing': <ShopOutlined />,
      'ai': <RobotOutlined />,
      'ai_agent': <RobotOutlined />,
      'communication': <MessageOutlined />,
      'message': <MessageOutlined />,
      'finance': <DollarOutlined />,
      'analytics': <PieChartOutlined />,
      'integration': <ApiOutlined />,
      'utility': <ToolOutlined />
    };

    return iconMap[category] || <AppstoreOutlined />;
  };

  // 获取类型标签
  const getTypeTag = (type) => {
    if (type === 'marketplace') {
      return <Tag color="blue">市场插件</Tag>;
    } else if (type === 'ai') {
      return <Tag color="green">AI智能体</Tag>;
    } else if (type === 'system') {
      return <Tag color="purple">系统插件</Tag>;
    }
    return <Tag>未知类型</Tag>;
  };

  // 初始化
  useEffect(() => {
    fetchPlugins();
  }, []);

  // 渲染插件列表
  const renderPluginList = () => {
    if (filteredPlugins.length === 0) {
      return <Empty description="没有找到符合条件的插件" />;
    }

    return (
      <List
        grid={{ gutter: 16, column: 4, xs: 1, sm: 2, md: 3, lg: 4 }}
        dataSource={filteredPlugins}
        renderItem={plugin => {
          const isInstalled = plugin.is_installed;

          return (
            <List.Item>
              <Card
                hoverable
                cover={
                  <div style={{
                    height: 120,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    padding: 16,
                    background: '#f5f5f5'
                  }}>
                    {plugin.icon_url ? (
                      <img
                        src={plugin.icon_url}
                        alt={plugin.name}
                        style={{ maxHeight: '100%', maxWidth: '100%' }}
                      />
                    ) : (
                      <div style={{ fontSize: 48, color: '#1890ff' }}>
                        {getCategoryIcon(plugin.category)}
                      </div>
                    )}
                  </div>
                }
                actions={[
                  <Tooltip title="查看详情">
                    <Button
                      type="link"
                      icon={<InfoCircleOutlined />}
                      onClick={() => handleViewPlugin(plugin)}
                    >
                      详情
                    </Button>
                  </Tooltip>,
                  isInstalled ? (
                    <Tooltip title="配置插件">
                      <Button
                        type="link"
                        icon={<SettingOutlined />}
                        onClick={() => handleConfigPlugin(plugin)}
                      >
                        配置
                      </Button>
                    </Tooltip>
                  ) : (
                    <Tooltip title="安装插件">
                      <Button
                        type="link"
                        icon={<DownloadOutlined />}
                        onClick={() => handleInstallPlugin(plugin)}
                      >
                        安装
                      </Button>
                    </Tooltip>
                  ),
                  isInstalled && (
                    <Tooltip title="卸载插件">
                      <Button
                        type="link"
                        danger
                        icon={<CloseCircleOutlined />}
                        onClick={() => handleUninstallPlugin(plugin)}
                      >
                        卸载
                      </Button>
                    </Tooltip>
                  )
                ].filter(Boolean)}
              >
                <Card.Meta
                  title={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Tooltip title={plugin.name}>
                        <span style={{
                          maxWidth: '70%',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}>
                          {plugin.name}
                        </span>
                      </Tooltip>
                      {getTypeTag(plugin.type)}
                    </div>
                  }
                  description={
                    <div>
                      <Paragraph ellipsis={{ rows: 2 }} style={{ height: 40 }}>
                        {plugin.description}
                      </Paragraph>
                      <div style={{ marginTop: 8 }}>
                        <Space>
                          <Tag color="blue">{getCategoryLabel(plugin.category)}</Tag>
                          <Tag>v{plugin.version}</Tag>
                          {plugin.price > 0 ? (
                            <Tag color="orange">¥{plugin.price}</Tag>
                          ) : (
                            <Tag color="green">免费</Tag>
                          )}
                        </Space>
                      </div>
                    </div>
                  }
                />
              </Card>
            </List.Item>
          );
        }}
      />
    );
  };

  return (
    <div className="unified-plugin-market">
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>统一插件市场</span>
            <Space>
              <Button
                icon={<ShoppingCartOutlined />}
                onClick={() => navigate("/project/plugins/orders")}
              >
                我的插件订单
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchPlugins}
              >
                刷新
              </Button>
            </Space>
          </div>
        }
      >
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Search
                placeholder="搜索插件"
                allowClear
                enterButton={<SearchOutlined />}
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Select
                placeholder="选择插件类型"
                style={{ width: '100%' }}
                value={activeType}
                onChange={handleTypeChange}
              >
                <Option value="all">所有类型</Option>
                <Option value="marketplace">市场插件</Option>
                <Option value="ai">AI智能体</Option>
                <Option value="system">系统插件</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Select
                placeholder="选择插件分类"
                style={{ width: '100%' }}
                value={activeCategory}
                onChange={handleCategoryChange}
              >
                <Option value="all">所有分类</Option>
                {categories.map(category => (
                  <Option key={category.value} value={category.value}>
                    {category.label}
                  </Option>
                ))}
              </Select>
            </Col>
          </Row>
        </div>

        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab="全部插件" key="all">
            {renderPluginList()}
          </TabPane>
          <TabPane tab="已安装" key="installed">
            {renderPluginList()}
          </TabPane>
          <TabPane tab="市场插件" key="marketplace">
            {renderPluginList()}
          </TabPane>
          <TabPane tab="AI智能体" key="ai">
            {renderPluginList()}
          </TabPane>
          <TabPane tab="系统插件" key="system">
            {renderPluginList()}
          </TabPane>
        </Tabs>
      </Card>

      {/* 插件详情模态框 */}
      <Modal
        title="插件详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="back" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
          selectedPlugin?.is_installed ? (
            <Button
              key="config"
              type="primary"
              onClick={() => {
                setDetailModalVisible(false);
                handleConfigPlugin(selectedPlugin);
              }}
            >
              配置插件
            </Button>
          ) : (
            <Button
              key="install"
              type="primary"
              onClick={() => {
                setDetailModalVisible(false);
                handleInstallPlugin(selectedPlugin);
              }}
            >
              安装插件
            </Button>
          )
        ]}
        width={700}
      >
        {selectedPlugin && (
          <div>
            <div style={{ display: 'flex', marginBottom: 16 }}>
              <div style={{
                width: 100,
                height: 100,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                background: '#f5f5f5',
                marginRight: 16
              }}>
                {selectedPlugin.icon_url ? (
                  <img
                    src={selectedPlugin.icon_url}
                    alt={selectedPlugin.name}
                    style={{ maxHeight: '100%', maxWidth: '100%' }}
                  />
                ) : (
                  <div style={{ fontSize: 48, color: '#1890ff' }}>
                    {getCategoryIcon(selectedPlugin.category)}
                  </div>
                )}
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                  <Title level={4} style={{ margin: 0, marginRight: 8 }}>{selectedPlugin.name}</Title>
                  {getTypeTag(selectedPlugin.type)}
                </div>
                <Space style={{ marginBottom: 8 }}>
                  <Tag color="blue">{getCategoryLabel(selectedPlugin.category)}</Tag>
                  <Tag>v{selectedPlugin.version}</Tag>
                  {selectedPlugin.price > 0 ? (
                    <Tag color="orange">¥{selectedPlugin.price}</Tag>
                  ) : (
                    <Tag color="green">免费</Tag>
                  )}
                  {selectedPlugin.is_installed && (
                    <Tag color="green">已安装</Tag>
                  )}
                </Space>
                <div>
                  <Text type="secondary">作者: {selectedPlugin.author || '未知'}</Text>
                </div>
              </div>
            </div>

            <Divider />

            <Title level={5}>插件描述</Title>
            <Paragraph>{selectedPlugin.description}</Paragraph>

            <Divider />

            <Title level={5}>功能特性</Title>
            <ul>
              {selectedPlugin.features?.map((feature, index) => (
                <li key={index}>{feature}</li>
              )) || <li>暂无功能特性描述</li>}
            </ul>

            {selectedPlugin.website && (
              <>
                <Divider />
                <Title level={5}>官方网站</Title>
                <a href={selectedPlugin.website} target="_blank" rel="noopener noreferrer">
                  {selectedPlugin.website}
                </a>
              </>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default UnifiedPluginMarket;
