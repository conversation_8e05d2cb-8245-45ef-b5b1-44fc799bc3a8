#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_
import uuid
from datetime import datetime
from sqlalchemy.orm import selectinload

from db.database import get_db
from models.tenant import Tenant
from models.project import Project
from models.plugin import Plugin, TenantPlugin
from .models.models import DingTalkSettings, DingTalkWebhook
from models.user import User

# 初始化日志
logger = logging.getLogger(__name__)

# 插件信息
PLUGIN_INFO = {
    "code": "dingtalk",
    "name": "钉钉机器人",
    "description": "集成钉钉机器人，实现消息推送、群聊管理等功能",
    "version": "1.0.0",
    "type": "marketplace",
    "category": "ai_agent",
    "icon_url": "/static/plugins/dingtalk/icon.png",
    "price": 0,
    "author": "系统",
    "homepage": None,
    "features": [
        "消息推送",
        "群聊管理",
        "自定义机器人",
        "定时任务",
        "事件通知"
    ]
}

async def initialize_plugin(tenant_id=None, project_id=None):
    """初始化钉钉机器人插件数据库"""
    logger.info("初始化钉钉机器人插件数据库")

    async for db in get_db():
        try:
            # 检查插件是否已注册
            plugin_query = select(Plugin).where(Plugin.code == PLUGIN_INFO["code"])
            result = await db.execute(plugin_query)
            plugin = result.scalar_one_or_none()

            if not plugin:
                # 注册插件
                plugin = Plugin(
                    id=uuid.uuid4(),
                    code=PLUGIN_INFO["code"],
                    name=PLUGIN_INFO["name"],
                    description=PLUGIN_INFO["description"],
                    version=PLUGIN_INFO["version"],
                    type=PLUGIN_INFO["type"],
                    category=PLUGIN_INFO["category"],
                    icon_url=PLUGIN_INFO["icon_url"],
                    price=PLUGIN_INFO["price"],
                    is_system=False,
                    is_active=True,
                    author=PLUGIN_INFO["author"],
                    homepage=PLUGIN_INFO["homepage"],
                    installation_path="plugins.dingtalk",
                    entry_point="initialize",
                    created_at=datetime.now(),
                    settings_schema={
                        "properties": {
                            "enable_dingtalk": {
                                "type": "boolean",
                                "title": "启用钉钉通知",
                                "default": True
                            },
                            "notification_level": {
                                "type": "string",
                                "title": "通知级别",
                                "enum": ["all", "high_only", "custom"],
                                "enumNames": ["所有通知", "仅高优先级", "自定义"],
                                "default": "all"
                            },
                            "retry_count": {
                                "type": "integer",
                                "title": "重试次数",
                                "minimum": 0,
                                "maximum": 10,
                                "default": 3
                            },
                            "retry_interval": {
                                "type": "integer",
                                "title": "重试间隔(秒)",
                                "minimum": 10,
                                "maximum": 300,
                                "default": 60
                            },
                            "default_template": {
                                "type": "string",
                                "title": "默认消息模板",
                                "format": "textarea",
                                "default": "### {{title}}\n\n{{content}}\n\n> 来自系统通知 - {{time}}"
                            }
                        }
                    },
                    features=["system_notification", "user_sync", "group_chat"]
                )
                db.add(plugin)
                await db.commit()
                logger.info(f"钉钉机器人插件注册成功: {plugin.id}")
            else:
                # 更新插件信息
                plugin.name = PLUGIN_INFO["name"]
                plugin.description = PLUGIN_INFO["description"]
                plugin.version = PLUGIN_INFO["version"]
                plugin.type = PLUGIN_INFO["type"]
                plugin.category = PLUGIN_INFO["category"]
                plugin.icon_url = PLUGIN_INFO["icon_url"]
                plugin.price = PLUGIN_INFO["price"]
                plugin.author = PLUGIN_INFO["author"]
                plugin.homepage = PLUGIN_INFO["homepage"]
                plugin.installation_path = "plugins.dingtalk"
                plugin.entry_point = "initialize"
                plugin.is_active = True
                plugin.last_updated_at = datetime.now()

                await db.commit()
                logger.info(f"更新了钉钉机器人插件: {plugin.id}")

            # 如果指定了租户和项目，则为其初始化插件设置
            if tenant_id and project_id:
                await initialize_tenant_plugin(db, tenant_id, project_id, plugin.id)

        except Exception as e:
            logger.error(f"初始化钉钉机器人插件失败: {str(e)}")
            await db.rollback()
            raise
        finally:
            await db.close()

async def initialize_tenant_plugin(db: AsyncSession, tenant_id: uuid.UUID, project_id: uuid.UUID, plugin_id: uuid.UUID):
    """为指定租户和项目初始化插件设置"""
    try:
        # 检查租户插件是否已安装
        tenant_plugin_query = select(TenantPlugin).where(
            and_(
                TenantPlugin.tenant_id == tenant_id,
                TenantPlugin.plugin_id == plugin_id
            )
        )
        result = await db.execute(tenant_plugin_query)
        tenant_plugin = result.scalar_one_or_none()

        if not tenant_plugin:
            # 安装插件
            plugin_query = select(Plugin).where(Plugin.id == plugin_id)
            result = await db.execute(plugin_query)
            plugin = result.scalar_one_or_none()

            if plugin:
                tenant_plugin = TenantPlugin(
                    id=uuid.uuid4(),
                    tenant_id=tenant_id,
                    plugin_id=plugin_id,
                    status="active",
                    version=plugin.version,
                    settings={}
                )
                db.add(tenant_plugin)
                await db.commit()
                logger.info(f"租户 {tenant_id} 安装钉钉机器人插件成功")

        # 检查项目插件设置是否已初始化
        settings_query = select(DingTalkSettings).where(
            and_(
                DingTalkSettings.tenant_id == tenant_id,
                DingTalkSettings.project_id == project_id
            )
        )
        result = await db.execute(settings_query)
        settings = result.scalar_one_or_none()

        if not settings:
            # 初始化插件设置
            settings = DingTalkSettings(
                id=uuid.uuid4(),
                tenant_id=tenant_id,
                project_id=project_id,
                enable_dingtalk=True,
                notification_level="all",
                retry_count=3,
                retry_interval=60,
                default_template="### {{title}}\n\n{{content}}\n\n> 来自系统通知 - {{time}}"
            )
            db.add(settings)
            await db.commit()
            logger.info(f"项目 {project_id} 初始化钉钉机器人插件设置成功")

        # 创建默认Webhook
        webhook_query = select(DingTalkWebhook).where(
            and_(
                DingTalkWebhook.tenant_id == tenant_id,
                DingTalkWebhook.project_id == project_id
            )
        )
        result = await db.execute(webhook_query)
        webhook = result.scalar_one_or_none()

        if not webhook:
            # 获取租户管理员 - 修复懒加载问题
            tenant_query = select(Tenant).options(selectinload(Tenant.users)).where(Tenant.id == tenant_id)
            result = await db.execute(tenant_query)
            tenant = result.scalar_one_or_none()

            admin_id = None
            if tenant:
                # 直接查询租户的用户
                user_query = select(User).where(User.tenant_id == tenant_id).limit(1)
                user_result = await db.execute(user_query)
                admin_user = user_result.scalar_one_or_none()
                if admin_user:
                    admin_id = admin_user.id

            if admin_id:
                webhook = DingTalkWebhook(
                    id=uuid.uuid4(),
                    tenant_id=tenant_id,
                    project_id=project_id,
                    name="默认机器人",
                    webhook_url="",  # 需要用户配置
                    enabled=False,  # 默认禁用，等待用户配置
                    message_template="### {{title}}\n\n{{content}}\n\n> 来自系统通知 - {{time}}",
                    notification_types=["all"],
                    created_by=admin_id
                )
                db.add(webhook)
                await db.commit()
                logger.info(f"项目 {project_id} 创建默认钉钉机器人Webhook成功")

    except Exception as e:
        logger.error(f"初始化租户插件失败: {str(e)}")
        await db.rollback()
        raise

if __name__ == "__main__":
    asyncio.run(initialize_plugin())
