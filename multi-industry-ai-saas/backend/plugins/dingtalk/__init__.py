#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from fastapi import APIRouter, FastAPI
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker

# 导入API路由
from .api import router
from .api.admin import router as admin_router

# 导入数据库初始化
from .db_init import initialize_plugin

# 导入系统通知集成
from .integration import handle_system_notification

# 导入用户同步功能
from .utils.sync_users import sync_dingtalk_data

# 导出发送通知函数
from .utils.notification import send_notification_to_dingtalk

# 导入新功能
from .utils.token_manager import token_manager
from .utils.permission_manager import DingTalkPermissionManager

__all__ = [
    "router", 
    "admin_router",
    "send_notification_to_dingtalk", 
    "handle_system_notification", 
    "initialize_plugin", 
    "sync_dingtalk_data", 
    "initialize",
    "token_manager",
    "DingTalkPermissionManager"
]

# 插件版本
__version__ = "2.0.0"

# 插件信息
plugin_info = {
    "name": "钉钉智能机器人插件",
    "code": "dingtalk_robot",
    "description": "集成钉钉平台的智能机器人插件，支持消息通知、AI对话、知识库同步等功能。面向不同角色提供差异化功能：普通用户可进行AI对话和消息发送，管理员可配置插件设置、管理Webhook、同步知识库等。",
    "version": __version__,
    "author": "Retail AI SaaS",
    "category": "ai_agent",
    "price": 0,  # 免费插件
    "billing_cycle": "one_time",
    "is_system": True,
    "is_active": True,
    "requires_subscription": False,
    "dependencies": [
        {"name": "aiohttp", "version": ">=3.8.0", "required": True},
        {"name": "sqlalchemy", "version": ">=2.0.0", "required": True}
    ],
    "tables": [
        "dingtalk_webhooks",
        "dingtalk_groups",
        "dingtalk_user_mappings",
        "dingtalk_settings",
        "dingtalk_notification_logs",
        "dingtalk_knowledge_bases",
        "dingtalk_ai_integrations"
    ],
    "features": [
        "system_notification", 
        "user_sync", 
        "group_chat", 
        "ai_integration", 
        "knowledge_sync", 
        "role_based_access",
        "auto_token_management"
    ],
    "permissions": {
        "user": [
            "查看用户信息",
            "同步用户信息", 
            "AI聊天对话",
            "发送消息"
        ],
        "admin": [
            "管理插件设置",
            "管理Webhook配置",
            "查看通知日志",
            "同步钉钉知识库",
            "配置AI集成",
            "高级数据同步"
        ],
        "super_admin": [
            "插件系统配置",
            "系统集成管理"
        ]
    }
}

# 初始化日志
logger = logging.getLogger(__name__)

async def initialize(app: FastAPI = None, engine: Engine = None, db_session: sessionmaker = None):
    """
    插件初始化函数

    Args:
        app: FastAPI应用实例
        engine: 数据库引擎
        db_session: 数据库会话工厂
    """
    try:
        logger.info("正在初始化钉钉智能机器人插件...")

        # 初始化数据库
        success = await initialize_plugin()

        if success:
            logger.info("钉钉机器人插件数据库初始化成功")
        else:
            logger.warning("钉钉机器人插件数据库初始化失败")

        # 注册路由
        if app:
            # 注册基础功能路由
            app.include_router(router, prefix="/plugins")
            # 注册管理员功能路由
            app.include_router(admin_router, prefix="/plugins")
            logger.info("钉钉机器人插件路由注册成功")

        logger.info(f"钉钉智能机器人插件 v{__version__} 初始化完成")
        logger.info("支持功能：消息通知、AI对话、知识库同步、权限管理")
        return True
    except Exception as e:
        logger.error(f"钉钉机器人插件初始化失败: {str(e)}")
        return False
