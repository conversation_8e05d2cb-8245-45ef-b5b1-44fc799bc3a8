#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
插件系统入口

这个模块是插件系统的入口点，负责初始化插件系统和注册插件路由。
"""

import logging
from fastapi import APIRouter

from .base import plugin_manager
from .loader import plugin_loader

# 设置日志
logger = logging.getLogger(__name__)

# 创建插件总路由
plugin_router = APIRouter()

# 导入插件路由
from .marketing_game.api import router as marketing_game_router
from .dingtalk.api import router as dingtalk_router
from .sapi import router as sapi_router
from .ai_customer_service.api import router as ai_customer_service_router

# 注册插件路由
plugin_router.include_router(marketing_game_router, prefix="/marketing-game", tags=["营销游戏插件"])
plugin_router.include_router(dingtalk_router, prefix="/dingtalk")
plugin_router.include_router(sapi_router, prefix="/sapi", tags=["AI 智能体（AI 员工）"])
plugin_router.include_router(ai_customer_service_router, prefix="/ai-customer-service", tags=["AI 智能体 - 超级全能客服"])

# 导出 router 供主路由引用
router = plugin_router

# 导出插件管理器和加载器
__all__ = ["router", "plugin_manager", "plugin_loader", "initialize_plugins"]


async def initialize_plugins():
    """
    初始化插件系统

    发现并加载所有插件
    """
    logger.info("初始化插件系统")
    plugin_loader.discover_plugins()

    # 初始化 SAPI 插件数据库
    try:
        from .sapi import init_db
        await init_db()
        logger.info("SAPI 插件数据库初始化成功")
    except Exception as e:
        logger.error(f"SAPI 插件数据库初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        # 不重新抛出异常，允许应用程序继续启动
        # 但是记录错误，以便后续处理


# 注意：插件路由现在直接在 api/v1/project/__init__.py 中注册，不再需要这个函数