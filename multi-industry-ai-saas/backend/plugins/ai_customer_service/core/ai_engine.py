#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 客服引擎

集成项目现有的 AI 助理系统，提供智能对话、多模态处理、工具调用等功能。
"""

import logging
import uuid
import asyncio
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from services.ai.assistant_service import AIAssistantService
from services.ai.chat_service import AIChatService
from services.ai.knowledge_service import AIKnowledgeService
from schemas.ai.assistant import AIAssistantChatRequest
from .platform_adapter import PlatformMessage, PlatformResponse

logger = logging.getLogger(__name__)

class AICustomerServiceEngine:
    """AI 客服引擎"""
    
    def __init__(self):
        self.default_assistant_id = None
        self.default_model_id = None
        self.default_config_id = None
        self.session_threads: Dict[str, uuid.UUID] = {}  # 会话ID -> 线程ID映射
        
    async def initialize(self):
        """初始化 AI 引擎"""
        try:
            logger.info("正在初始化 AI 客服引擎...")
            
            # 这里可以设置默认的AI助手配置
            # 实际使用时需要从配置中读取
            
            logger.info("AI 客服引擎初始化完成")
            
        except Exception as e:
            logger.error(f"AI 客服引擎初始化失败: {e}")
            raise
            
    async def shutdown(self):
        """关闭 AI 引擎"""
        logger.info("AI 客服引擎已关闭")
        
    async def process_message(
        self,
        db: AsyncSession,
        message: PlatformMessage,
        session_id: str,
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
        assistant_id: Optional[uuid.UUID] = None,
        use_knowledge_base: bool = True,
        knowledge_base_ids: Optional[List[str]] = None
    ) -> Tuple[PlatformResponse, Dict[str, Any]]:
        """
        处理用户消息并生成AI回复
        
        Args:
            db: 数据库会话
            message: 平台消息
            session_id: 会话ID
            project_id: 项目ID
            user_id: 用户ID
            assistant_id: AI助手ID
            use_knowledge_base: 是否使用知识库
            knowledge_base_ids: 知识库ID列表
            
        Returns:
            Tuple[PlatformResponse, Dict[str, Any]]: AI回复和处理元数据
        """
        try:
            start_time = datetime.utcnow()
            
            # 获取或创建对话线程
            thread_id = await self._get_or_create_thread(
                db, session_id, project_id, user_id
            )
            
            # 准备聊天请求
            chat_request = AIAssistantChatRequest(
                assistant_id=assistant_id or self.default_assistant_id,
                thread_id=thread_id,
                message=message.content,
                use_knowledge_base=use_knowledge_base,
                knowledge_base_ids=knowledge_base_ids or [],
                attachments=message.attachments
            )
            
            # 调用AI助手服务
            chat_response = await AIAssistantService.chat_with_assistant(
                db=db,
                chat_request=chat_request,
                user_id=user_id or uuid.uuid4()  # 如果没有用户ID，使用临时ID
            )
            
            # 计算处理时间
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            # 构建平台响应
            platform_response = PlatformResponse(
                message_type="text",
                content=chat_response.get("content", "抱歉，我暂时无法回答您的问题。"),
                attachments=[],
                meta_data={
                    "ai_response": True,
                    "processing_time": processing_time,
                    "model_used": chat_response.get("model_used"),
                    "thread_id": str(thread_id)
                }
            )
            
            # 处理元数据
            processing_metadata = {
                "processing_time": processing_time,
                "model_used": chat_response.get("model_used"),
                "confidence_score": chat_response.get("confidence_score", 0.8),
                "knowledge_sources": chat_response.get("knowledge_sources", []),
                "rag_used": len(chat_response.get("knowledge_sources", [])) > 0,
                "thread_id": str(thread_id),
                "tools_used": chat_response.get("tools_used", [])
            }
            
            return platform_response, processing_metadata
            
        except Exception as e:
            logger.error(f"AI消息处理失败: {e}")
            
            # 返回错误响应
            error_response = PlatformResponse(
                message_type="text",
                content="抱歉，系统暂时繁忙，请稍后再试。",
                attachments=[],
                metadata={"error": True, "error_message": str(e)}
            )
            
            error_metadata = {
                "error": True,
                "error_message": str(e),
                "processing_time": 0.0
            }
            
            return error_response, error_metadata
            
    async def _get_or_create_thread(
        self,
        db: AsyncSession,
        session_id: str,
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID]
    ) -> uuid.UUID:
        """获取或创建对话线程"""
        try:
            # 检查是否已有线程
            if session_id in self.session_threads:
                return self.session_threads[session_id]
                
            # 创建新线程
            # 这里应该调用AI助手服务创建线程
            # 暂时生成一个UUID作为线程ID
            thread_id = uuid.uuid4()
            
            # 缓存线程ID
            self.session_threads[session_id] = thread_id
            
            logger.debug(f"为会话 {session_id} 创建新线程 {thread_id}")
            
            return thread_id
            
        except Exception as e:
            logger.error(f"创建对话线程失败: {e}")
            # 返回一个临时线程ID
            return uuid.uuid4()
            
    async def analyze_message_intent(
        self,
        message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        分析消息意图
        
        Args:
            message: 用户消息
            context: 上下文信息
            
        Returns:
            Dict[str, Any]: 意图分析结果
        """
        try:
            # 这里可以集成意图识别模型
            # 暂时使用简单的关键词匹配
            
            intent_keywords = {
                "complaint": ["投诉", "不满", "问题", "故障", "错误"],
                "inquiry": ["咨询", "了解", "请问", "怎么", "如何"],
                "purchase": ["购买", "下单", "支付", "价格", "优惠"],
                "support": ["帮助", "支持", "协助", "解决"],
                "greeting": ["你好", "hello", "hi", "您好"]
            }
            
            message_lower = message.lower()
            detected_intents = []
            
            for intent, keywords in intent_keywords.items():
                for keyword in keywords:
                    if keyword in message_lower:
                        detected_intents.append(intent)
                        break
                        
            # 如果没有检测到特定意图，默认为咨询
            if not detected_intents:
                detected_intents = ["inquiry"]
                
            return {
                "intents": detected_intents,
                "primary_intent": detected_intents[0],
                "confidence": 0.8,
                "entities": [],  # 可以添加实体识别
                "sentiment": "neutral"  # 可以添加情感分析
            }
            
        except Exception as e:
            logger.error(f"意图分析失败: {e}")
            return {
                "intents": ["inquiry"],
                "primary_intent": "inquiry",
                "confidence": 0.5,
                "entities": [],
                "sentiment": "neutral"
            }
            
    async def generate_smart_reply_suggestions(
        self,
        message: str,
        intent: str,
        context: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        生成智能回复建议
        
        Args:
            message: 用户消息
            intent: 消息意图
            context: 上下文信息
            
        Returns:
            List[str]: 回复建议列表
        """
        try:
            # 根据意图生成回复建议
            suggestions = []
            
            if intent == "greeting":
                suggestions = [
                    "您好！很高兴为您服务，请问有什么可以帮助您的吗？",
                    "您好！我是AI客服助手，有什么问题可以随时咨询我。",
                    "欢迎！请告诉我您需要什么帮助。"
                ]
            elif intent == "complaint":
                suggestions = [
                    "非常抱歉给您带来不便，我会立即为您处理这个问题。",
                    "我理解您的困扰，让我来帮您解决这个问题。",
                    "感谢您的反馈，我会认真处理您的投诉。"
                ]
            elif intent == "inquiry":
                suggestions = [
                    "我来为您详细介绍一下相关信息。",
                    "关于您的问题，我为您查询一下相关资料。",
                    "让我为您提供准确的信息。"
                ]
            elif intent == "purchase":
                suggestions = [
                    "我来为您介绍我们的产品和优惠活动。",
                    "关于购买，我可以为您提供详细的指导。",
                    "让我为您推荐最适合的产品。"
                ]
            else:
                suggestions = [
                    "我来为您处理这个问题。",
                    "让我帮助您解决这个需求。",
                    "我会尽力为您提供帮助。"
                ]
                
            return suggestions[:3]  # 返回前3个建议
            
        except Exception as e:
            logger.error(f"生成回复建议失败: {e}")
            return ["我来为您处理这个问题。"]
            
    async def evaluate_response_quality(
        self,
        user_message: str,
        ai_response: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        评估AI回复质量
        
        Args:
            user_message: 用户消息
            ai_response: AI回复
            context: 上下文信息
            
        Returns:
            Dict[str, Any]: 质量评估结果
        """
        try:
            # 简单的质量评估逻辑
            quality_score = 0.8  # 基础分数
            
            # 检查回复长度
            if len(ai_response) < 10:
                quality_score -= 0.2
            elif len(ai_response) > 500:
                quality_score -= 0.1
                
            # 检查是否包含道歉或礼貌用语
            polite_words = ["请", "谢谢", "抱歉", "您好", "感谢"]
            if any(word in ai_response for word in polite_words):
                quality_score += 0.1
                
            # 检查是否回答了问题
            if "不知道" in ai_response or "无法回答" in ai_response:
                quality_score -= 0.3
                
            # 确保分数在0-1之间
            quality_score = max(0.0, min(1.0, quality_score))
            
            return {
                "quality_score": quality_score,
                "relevance": quality_score,
                "helpfulness": quality_score,
                "politeness": quality_score + 0.1 if quality_score > 0.7 else quality_score,
                "completeness": quality_score,
                "suggestions": []
            }
            
        except Exception as e:
            logger.error(f"回复质量评估失败: {e}")
            return {
                "quality_score": 0.5,
                "relevance": 0.5,
                "helpfulness": 0.5,
                "politeness": 0.5,
                "completeness": 0.5,
                "suggestions": []
            }
