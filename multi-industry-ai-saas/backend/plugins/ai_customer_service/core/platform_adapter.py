#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
多平台适配器管理器

基于 LangBot 的架构设计，支持多种即时通信平台的客服机器人适配。
"""

import logging
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class PlatformType(Enum):
    """支持的平台类型"""
    WECHAT = "wechat"  # 个人微信
    WECHAT_MP = "wechat_mp"  # 微信公众号
    WORK_WECHAT = "work_wechat"  # 企业微信
    DINGTALK = "dingtalk"  # 钉钉
    FEISHU = "feishu"  # 飞书
    QQ = "qq"  # QQ
    TELEGRAM = "telegram"  # Telegram
    DISCORD = "discord"  # Discord

@dataclass
class PlatformMessage:
    """平台消息数据结构"""
    platform_type: str
    platform_user_id: str
    user_name: Optional[str]
    message_type: str  # text, image, file, voice, video
    content: str
    attachments: List[Dict[str, Any]]
    timestamp: float
    session_id: str
    metadata: Dict[str, Any]

@dataclass
class PlatformResponse:
    """平台响应数据结构"""
    message_type: str
    content: str
    attachments: List[Dict[str, Any]]
    metadata: Dict[str, Any]

class BasePlatformAdapter(ABC):
    """平台适配器基类"""
    
    def __init__(self, platform_type: str, config: Dict[str, Any]):
        self.platform_type = platform_type
        self.config = config
        self.is_connected = False
        self.message_handlers: List[Callable] = []
        
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化平台连接"""
        pass
        
    @abstractmethod
    async def shutdown(self) -> None:
        """关闭平台连接"""
        pass
        
    @abstractmethod
    async def send_message(self, user_id: str, response: PlatformResponse) -> bool:
        """发送消息到平台"""
        pass
        
    @abstractmethod
    async def get_user_info(self, user_id: str) -> Dict[str, Any]:
        """获取用户信息"""
        pass
        
    def add_message_handler(self, handler: Callable):
        """添加消息处理器"""
        self.message_handlers.append(handler)
        
    async def handle_message(self, message: PlatformMessage):
        """处理接收到的消息"""
        for handler in self.message_handlers:
            try:
                await handler(message)
            except Exception as e:
                logger.error(f"消息处理器执行失败: {e}")

class WeChatAdapter(BasePlatformAdapter):
    """个人微信适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(PlatformType.WECHAT.value, config)
        self.client = None
        
    async def initialize(self) -> bool:
        """初始化微信连接"""
        try:
            # 这里集成微信机器人库（如 WeChatPad）
            logger.info("正在初始化个人微信适配器...")
            
            # 模拟初始化过程
            await asyncio.sleep(0.1)
            
            self.is_connected = True
            logger.info("个人微信适配器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"个人微信适配器初始化失败: {e}")
            return False
            
    async def shutdown(self) -> None:
        """关闭微信连接"""
        if self.client:
            # 关闭连接
            pass
        self.is_connected = False
        logger.info("个人微信适配器已关闭")
        
    async def send_message(self, user_id: str, response: PlatformResponse) -> bool:
        """发送消息到微信"""
        try:
            # 实现微信消息发送
            logger.debug(f"发送微信消息到 {user_id}: {response.content[:50]}...")
            return True
        except Exception as e:
            logger.error(f"发送微信消息失败: {e}")
            return False
            
    async def get_user_info(self, user_id: str) -> Dict[str, Any]:
        """获取微信用户信息"""
        try:
            # 实现用户信息获取
            return {
                "user_id": user_id,
                "nickname": "微信用户",
                "avatar": "",
                "platform": self.platform_type
            }
        except Exception as e:
            logger.error(f"获取微信用户信息失败: {e}")
            return {}

class WeChatMPAdapter(BasePlatformAdapter):
    """微信公众号适配器"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(PlatformType.WECHAT_MP.value, config)
        self.app_id = config.get("app_id")
        self.app_secret = config.get("app_secret")
        self.token = config.get("token")
        self.encoding_aes_key = config.get("encoding_aes_key")
        self.pending_responses = {}  # 存储待发送的响应（处理15秒限制）

    async def initialize(self) -> bool:
        """初始化微信公众号连接"""
        try:
            logger.info("正在初始化微信公众号适配器...")

            # 验证配置
            if not all([self.app_id, self.app_secret, self.token]):
                raise ValueError("微信公众号配置不完整")

            # 获取access_token
            await self._refresh_access_token()

            self.is_connected = True
            logger.info("微信公众号适配器初始化成功")
            return True

        except Exception as e:
            logger.error(f"微信公众号适配器初始化失败: {e}")
            return False

    async def shutdown(self) -> None:
        """关闭微信公众号连接"""
        self.is_connected = False
        logger.info("微信公众号适配器已关闭")

    async def send_message(self, user_id: str, response: PlatformResponse) -> bool:
        """发送消息到微信公众号"""
        try:
            # 微信公众号有15秒响应限制，需要特殊处理
            if response.metadata.get("immediate_response"):
                # 立即响应（15秒内）
                return await self._send_immediate_response(user_id, response)
            else:
                # 异步响应（通过客服消息接口）
                return await self._send_customer_service_message(user_id, response)

        except Exception as e:
            logger.error(f"发送微信公众号消息失败: {e}")
            return False

    async def _send_immediate_response(self, user_id: str, response: PlatformResponse) -> bool:
        """发送立即响应（15秒内）"""
        try:
            # 这里应该返回给微信服务器的XML响应
            # 实际实现中，这个方法会被webhook处理器调用
            logger.debug(f"准备立即响应微信公众号消息到 {user_id}: {response.content[:50]}...")

            # 存储响应，等待webhook处理器获取
            self.pending_responses[user_id] = {
                "content": response.content,
                "message_type": response.message_type,
                "attachments": response.attachments,
                "timestamp": asyncio.get_event_loop().time()
            }

            return True

        except Exception as e:
            logger.error(f"发送立即响应失败: {e}")
            return False

    async def _send_customer_service_message(self, user_id: str, response: PlatformResponse) -> bool:
        """发送客服消息（异步）"""
        try:
            # 获取access_token
            access_token = await self._get_access_token()
            if not access_token:
                return False

            # 构建客服消息
            message_data = {
                "touser": user_id,
                "msgtype": "text",
                "text": {
                    "content": response.content
                }
            }

            # 如果有图片附件
            if response.attachments:
                for attachment in response.attachments:
                    if attachment.get("type") == "image":
                        message_data = {
                            "touser": user_id,
                            "msgtype": "image",
                            "image": {
                                "media_id": attachment.get("media_id")
                            }
                        }
                        break

            # 发送客服消息
            import aiohttp
            async with aiohttp.ClientSession() as session:
                url = f"https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token={access_token}"
                async with session.post(url, json=message_data) as resp:
                    result = await resp.json()

                    if result.get("errcode") == 0:
                        logger.debug(f"微信公众号客服消息发送成功到 {user_id}")
                        return True
                    else:
                        logger.error(f"微信公众号客服消息发送失败: {result}")
                        return False

        except Exception as e:
            logger.error(f"发送客服消息失败: {e}")
            return False

    async def _refresh_access_token(self):
        """刷新access_token"""
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                url = f"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={self.app_id}&secret={self.app_secret}"
                async with session.get(url) as resp:
                    result = await resp.json()

                    if "access_token" in result:
                        self.access_token = result["access_token"]
                        self.token_expires_at = asyncio.get_event_loop().time() + result.get("expires_in", 7200) - 300  # 提前5分钟刷新
                        logger.debug("微信公众号access_token刷新成功")
                    else:
                        logger.error(f"获取微信公众号access_token失败: {result}")

        except Exception as e:
            logger.error(f"刷新access_token失败: {e}")

    async def _get_access_token(self) -> str:
        """获取有效的access_token"""
        try:
            # 检查token是否过期
            if not hasattr(self, 'access_token') or asyncio.get_event_loop().time() > getattr(self, 'token_expires_at', 0):
                await self._refresh_access_token()

            return getattr(self, 'access_token', '')

        except Exception as e:
            logger.error(f"获取access_token失败: {e}")
            return ""

    async def get_user_info(self, user_id: str) -> Dict[str, Any]:
        """获取微信公众号用户信息"""
        try:
            access_token = await self._get_access_token()
            if not access_token:
                return {}

            import aiohttp
            async with aiohttp.ClientSession() as session:
                url = f"https://api.weixin.qq.com/cgi-bin/user/info?access_token={access_token}&openid={user_id}&lang=zh_CN"
                async with session.get(url) as resp:
                    result = await resp.json()

                    if result.get("errcode"):
                        logger.error(f"获取微信公众号用户信息失败: {result}")
                        return {}

                    return {
                        "user_id": user_id,
                        "nickname": result.get("nickname", "微信用户"),
                        "avatar": result.get("headimgurl", ""),
                        "city": result.get("city", ""),
                        "province": result.get("province", ""),
                        "country": result.get("country", ""),
                        "platform": self.platform_type
                    }

        except Exception as e:
            logger.error(f"获取微信公众号用户信息失败: {e}")
            return {}

    def get_pending_response(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取待发送的响应（用于webhook处理器）"""
        return self.pending_responses.pop(user_id, None)

class WorkWeChatAdapter(BasePlatformAdapter):
    """企业微信适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(PlatformType.WORK_WECHAT.value, config)
        self.corp_id = config.get("corp_id")
        self.agent_id = config.get("agent_id")
        self.secret = config.get("secret")
        
    async def initialize(self) -> bool:
        """初始化企业微信连接"""
        try:
            logger.info("正在初始化企业微信适配器...")
            
            # 验证配置
            if not all([self.corp_id, self.agent_id, self.secret]):
                raise ValueError("企业微信配置不完整")
                
            # 初始化企业微信API
            await asyncio.sleep(0.1)
            
            self.is_connected = True
            logger.info("企业微信适配器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"企业微信适配器初始化失败: {e}")
            return False
            
    async def shutdown(self) -> None:
        """关闭企业微信连接"""
        self.is_connected = False
        logger.info("企业微信适配器已关闭")
        
    async def send_message(self, user_id: str, response: PlatformResponse) -> bool:
        """发送消息到企业微信"""
        try:
            logger.debug(f"发送企业微信消息到 {user_id}: {response.content[:50]}...")
            return True
        except Exception as e:
            logger.error(f"发送企业微信消息失败: {e}")
            return False
            
    async def get_user_info(self, user_id: str) -> Dict[str, Any]:
        """获取企业微信用户信息"""
        try:
            return {
                "user_id": user_id,
                "name": "企业微信用户",
                "department": "",
                "platform": self.platform_type
            }
        except Exception as e:
            logger.error(f"获取企业微信用户信息失败: {e}")
            return {}

class DingTalkAdapter(BasePlatformAdapter):
    """钉钉适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(PlatformType.DINGTALK.value, config)
        self.app_key = config.get("app_key")
        self.app_secret = config.get("app_secret")
        
    async def initialize(self) -> bool:
        """初始化钉钉连接"""
        try:
            logger.info("正在初始化钉钉适配器...")
            
            # 验证配置
            if not all([self.app_key, self.app_secret]):
                raise ValueError("钉钉配置不完整")
                
            # 初始化钉钉API
            await asyncio.sleep(0.1)
            
            self.is_connected = True
            logger.info("钉钉适配器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"钉钉适配器初始化失败: {e}")
            return False
            
    async def shutdown(self) -> None:
        """关闭钉钉连接"""
        self.is_connected = False
        logger.info("钉钉适配器已关闭")
        
    async def send_message(self, user_id: str, response: PlatformResponse) -> bool:
        """发送消息到钉钉"""
        try:
            logger.debug(f"发送钉钉消息到 {user_id}: {response.content[:50]}...")
            return True
        except Exception as e:
            logger.error(f"发送钉钉消息失败: {e}")
            return False
            
    async def get_user_info(self, user_id: str) -> Dict[str, Any]:
        """获取钉钉用户信息"""
        try:
            return {
                "user_id": user_id,
                "name": "钉钉用户",
                "department": "",
                "platform": self.platform_type
            }
        except Exception as e:
            logger.error(f"获取钉钉用户信息失败: {e}")
            return {}

class PlatformAdapterManager:
    """平台适配器管理器"""
    
    def __init__(self):
        self.adapters: Dict[str, BasePlatformAdapter] = {}
        self.adapter_classes = {
            PlatformType.WECHAT.value: WeChatAdapter,
            PlatformType.WECHAT_MP.value: WeChatMPAdapter,
            PlatformType.WORK_WECHAT.value: WorkWeChatAdapter,
            PlatformType.DINGTALK.value: DingTalkAdapter,
            # 可以继续添加其他平台适配器
        }
        
    async def initialize(self):
        """初始化所有适配器"""
        logger.info("正在初始化平台适配器管理器...")
        
        # 这里可以从配置中读取需要启用的平台
        # 暂时为演示目的，不实际初始化
        
        logger.info("平台适配器管理器初始化完成")
        
    async def add_adapter(self, platform_type: str, config: Dict[str, Any]) -> bool:
        """添加平台适配器"""
        try:
            if platform_type in self.adapter_classes:
                adapter_class = self.adapter_classes[platform_type]
                adapter = adapter_class(config)
                
                if await adapter.initialize():
                    self.adapters[platform_type] = adapter
                    logger.info(f"平台适配器 {platform_type} 添加成功")
                    return True
                else:
                    logger.error(f"平台适配器 {platform_type} 初始化失败")
                    return False
            else:
                logger.error(f"不支持的平台类型: {platform_type}")
                return False
                
        except Exception as e:
            logger.error(f"添加平台适配器失败: {e}")
            return False
            
    async def remove_adapter(self, platform_type: str):
        """移除平台适配器"""
        if platform_type in self.adapters:
            await self.adapters[platform_type].shutdown()
            del self.adapters[platform_type]
            logger.info(f"平台适配器 {platform_type} 已移除")
            
    async def send_message(self, platform_type: str, user_id: str, response: PlatformResponse) -> bool:
        """通过指定平台发送消息"""
        if platform_type in self.adapters:
            return await self.adapters[platform_type].send_message(user_id, response)
        else:
            logger.error(f"平台适配器 {platform_type} 不存在")
            return False
            
    async def get_user_info(self, platform_type: str, user_id: str) -> Dict[str, Any]:
        """获取用户信息"""
        if platform_type in self.adapters:
            return await self.adapters[platform_type].get_user_info(user_id)
        else:
            logger.error(f"平台适配器 {platform_type} 不存在")
            return {}
            
    def add_message_handler(self, handler: Callable):
        """为所有适配器添加消息处理器"""
        for adapter in self.adapters.values():
            adapter.add_message_handler(handler)
            
    async def shutdown(self):
        """关闭所有适配器"""
        logger.info("正在关闭所有平台适配器...")
        
        for platform_type in list(self.adapters.keys()):
            await self.remove_adapter(platform_type)
            
        logger.info("所有平台适配器已关闭")
