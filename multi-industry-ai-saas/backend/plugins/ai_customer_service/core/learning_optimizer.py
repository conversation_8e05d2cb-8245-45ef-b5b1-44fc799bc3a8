#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能学习优化器

基于客服对话数据，自动学习和优化AI回复质量、意图识别准确性、
业务流转效率等，实现系统的自我完善。
"""

import logging
import uuid
import json
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc

from ..models.customer_service import (
    CustomerServiceSession,
    CustomerServiceMessage,
    ConversationLog,
    CustomerServiceAnalytics
)

logger = logging.getLogger(__name__)

class LearningOptimizer:
    """智能学习优化器"""
    
    def __init__(self):
        self.learning_enabled = True
        self.optimization_threshold = 0.8  # 优化阈值
        self.learning_batch_size = 100
        self.optimization_rules = {}
        self.performance_metrics = {}
        
    async def analyze_conversation_patterns(
        self,
        db: AsyncSession,
        project_id: uuid.UUID,
        days: int = 7
    ) -> Dict[str, Any]:
        """
        分析对话模式，识别优化机会
        
        Args:
            db: 数据库会话
            project_id: 项目ID
            days: 分析天数
            
        Returns:
            Dict[str, Any]: 分析结果和优化建议
        """
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # 分析对话质量
            quality_analysis = await self._analyze_conversation_quality(
                db, project_id, start_date
            )
            
            # 分析意图识别准确性
            intent_analysis = await self._analyze_intent_accuracy(
                db, project_id, start_date
            )
            
            # 分析响应时间
            response_time_analysis = await self._analyze_response_times(
                db, project_id, start_date
            )
            
            # 分析客户满意度
            satisfaction_analysis = await self._analyze_customer_satisfaction(
                db, project_id, start_date
            )
            
            # 生成优化建议
            optimization_suggestions = await self._generate_optimization_suggestions(
                quality_analysis,
                intent_analysis,
                response_time_analysis,
                satisfaction_analysis
            )
            
            return {
                "analysis_period": {
                    "start_date": start_date.isoformat(),
                    "end_date": datetime.utcnow().isoformat(),
                    "days": days
                },
                "conversation_quality": quality_analysis,
                "intent_accuracy": intent_analysis,
                "response_times": response_time_analysis,
                "customer_satisfaction": satisfaction_analysis,
                "optimization_suggestions": optimization_suggestions,
                "overall_score": self._calculate_overall_score(
                    quality_analysis, intent_analysis, satisfaction_analysis
                )
            }
            
        except Exception as e:
            logger.error(f"分析对话模式失败: {e}")
            return {}
            
    async def _analyze_conversation_quality(
        self,
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: datetime
    ) -> Dict[str, Any]:
        """分析对话质量"""
        try:
            # 查询对话消息
            messages_query = select(CustomerServiceMessage).join(
                CustomerServiceSession
            ).where(
                and_(
                    CustomerServiceSession.project_id == project_id,
                    CustomerServiceMessage.created_at >= start_date,
                    CustomerServiceMessage.is_from_user == False  # AI回复
                )
            )
            
            messages_result = await db.execute(messages_query)
            ai_messages = messages_result.scalars().all()
            
            if not ai_messages:
                return {"total_messages": 0, "avg_quality": 0}
            
            # 分析质量指标
            total_messages = len(ai_messages)
            quality_scores = []
            confidence_scores = []
            rag_usage_count = 0
            
            for message in ai_messages:
                if message.confidence_score:
                    confidence_scores.append(message.confidence_score)
                    
                # 简单的质量评估
                quality_score = self._evaluate_message_quality(message)
                quality_scores.append(quality_score)
                
                if message.rag_used:
                    rag_usage_count += 1
            
            avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
            avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
            rag_usage_rate = rag_usage_count / total_messages if total_messages > 0 else 0
            
            # 识别低质量回复模式
            low_quality_patterns = await self._identify_low_quality_patterns(ai_messages)
            
            return {
                "total_messages": total_messages,
                "avg_quality": avg_quality,
                "avg_confidence": avg_confidence,
                "rag_usage_rate": rag_usage_rate,
                "low_quality_patterns": low_quality_patterns,
                "quality_distribution": self._get_quality_distribution(quality_scores)
            }
            
        except Exception as e:
            logger.error(f"分析对话质量失败: {e}")
            return {}
            
    def _evaluate_message_quality(self, message: CustomerServiceMessage) -> float:
        """评估单条消息质量"""
        try:
            quality_score = 0.5  # 基础分数
            
            # 基于置信度
            if message.confidence_score:
                quality_score += message.confidence_score * 0.3
                
            # 基于消息长度（适中的长度更好）
            content_length = len(message.content)
            if 20 <= content_length <= 200:
                quality_score += 0.1
            elif content_length > 500:
                quality_score -= 0.1
                
            # 基于是否使用RAG
            if message.rag_used and message.knowledge_sources:
                quality_score += 0.1
                
            # 基于处理时间（太快或太慢都不好）
            if message.processing_time:
                if 0.5 <= message.processing_time <= 5.0:
                    quality_score += 0.1
                elif message.processing_time > 10.0:
                    quality_score -= 0.1
                    
            # 检查是否包含礼貌用语
            polite_words = ["请", "谢谢", "抱歉", "您好", "感谢"]
            if any(word in message.content for word in polite_words):
                quality_score += 0.1
                
            # 检查是否包含错误回复
            error_indicators = ["不知道", "无法回答", "系统错误", "抱歉，我"]
            if any(indicator in message.content for indicator in error_indicators):
                quality_score -= 0.2
                
            return max(0.0, min(1.0, quality_score))
            
        except Exception as e:
            logger.error(f"评估消息质量失败: {e}")
            return 0.5
            
    async def _identify_low_quality_patterns(
        self, 
        messages: List[CustomerServiceMessage]
    ) -> List[Dict[str, Any]]:
        """识别低质量回复模式"""
        try:
            patterns = []
            
            # 分析常见的低质量回复
            low_quality_messages = [
                msg for msg in messages 
                if self._evaluate_message_quality(msg) < 0.6
            ]
            
            if len(low_quality_messages) > 10:
                # 分析低质量回复的共同特征
                common_phrases = {}
                for msg in low_quality_messages:
                    words = msg.content.split()
                    for word in words:
                        if len(word) > 2:
                            common_phrases[word] = common_phrases.get(word, 0) + 1
                
                # 找出高频出现的问题短语
                frequent_issues = [
                    {"phrase": phrase, "count": count}
                    for phrase, count in common_phrases.items()
                    if count >= 3
                ]
                
                if frequent_issues:
                    patterns.append({
                        "type": "frequent_low_quality_phrases",
                        "description": "发现频繁出现的低质量回复短语",
                        "details": frequent_issues[:10]
                    })
            
            # 分析响应时间异常
            slow_responses = [
                msg for msg in messages 
                if msg.processing_time and msg.processing_time > 10.0
            ]
            
            if len(slow_responses) > len(messages) * 0.1:  # 超过10%的回复很慢
                patterns.append({
                    "type": "slow_response_pattern",
                    "description": "发现响应时间过慢的模式",
                    "details": {
                        "slow_count": len(slow_responses),
                        "total_count": len(messages),
                        "percentage": len(slow_responses) / len(messages) * 100
                    }
                })
            
            return patterns
            
        except Exception as e:
            logger.error(f"识别低质量模式失败: {e}")
            return []
            
    async def _analyze_intent_accuracy(
        self,
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: datetime
    ) -> Dict[str, Any]:
        """分析意图识别准确性"""
        try:
            # 这里需要分析意图识别的准确性
            # 可以通过用户后续行为、满意度评分等来推断
            
            # 查询会话数据
            sessions_query = select(CustomerServiceSession).where(
                and_(
                    CustomerServiceSession.project_id == project_id,
                    CustomerServiceSession.started_at >= start_date
                )
            )
            
            sessions_result = await db.execute(sessions_query)
            sessions = sessions_result.scalars().all()
            
            if not sessions:
                return {"accuracy_score": 0, "total_sessions": 0}
            
            # 简单的准确性评估
            # 基于会话是否成功解决、客户满意度等
            successful_sessions = 0
            total_sessions = len(sessions)
            
            for session in sessions:
                # 如果会话状态为resolved且有满意度评分
                if (session.status == "resolved" and 
                    session.satisfaction_score and 
                    session.satisfaction_score >= 4.0):
                    successful_sessions += 1
                elif session.status == "resolved":
                    successful_sessions += 0.7  # 部分成功
            
            accuracy_score = successful_sessions / total_sessions if total_sessions > 0 else 0
            
            return {
                "accuracy_score": accuracy_score,
                "total_sessions": total_sessions,
                "successful_sessions": int(successful_sessions),
                "improvement_areas": self._identify_intent_improvement_areas(sessions)
            }
            
        except Exception as e:
            logger.error(f"分析意图准确性失败: {e}")
            return {}
            
    def _identify_intent_improvement_areas(
        self, 
        sessions: List[CustomerServiceSession]
    ) -> List[str]:
        """识别意图识别改进领域"""
        try:
            improvement_areas = []
            
            # 分析未解决的会话
            unresolved_sessions = [
                s for s in sessions 
                if s.status not in ["resolved", "closed"]
            ]
            
            if len(unresolved_sessions) > len(sessions) * 0.2:  # 超过20%未解决
                improvement_areas.append("提高复杂问题的处理能力")
            
            # 分析低满意度会话
            low_satisfaction_sessions = [
                s for s in sessions 
                if s.satisfaction_score and s.satisfaction_score < 3.0
            ]
            
            if len(low_satisfaction_sessions) > len(sessions) * 0.15:  # 超过15%低满意度
                improvement_areas.append("改进回复质量和准确性")
            
            # 分析人工介入率
            escalated_sessions = [
                s for s in sessions 
                if s.human_takeover_count > 0
            ]
            
            if len(escalated_sessions) > len(sessions) * 0.25:  # 超过25%需要人工介入
                improvement_areas.append("减少人工介入需求")
            
            return improvement_areas
            
        except Exception as e:
            logger.error(f"识别改进领域失败: {e}")
            return []
            
    async def _analyze_response_times(
        self,
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: datetime
    ) -> Dict[str, Any]:
        """分析响应时间"""
        try:
            # 查询AI消息的处理时间
            messages_query = select(CustomerServiceMessage).join(
                CustomerServiceSession
            ).where(
                and_(
                    CustomerServiceSession.project_id == project_id,
                    CustomerServiceMessage.created_at >= start_date,
                    CustomerServiceMessage.is_from_user == False,
                    CustomerServiceMessage.processing_time.isnot(None)
                )
            )
            
            messages_result = await db.execute(messages_query)
            messages = messages_result.scalars().all()
            
            if not messages:
                return {"avg_response_time": 0, "total_messages": 0}
            
            processing_times = [msg.processing_time for msg in messages]
            
            avg_time = sum(processing_times) / len(processing_times)
            min_time = min(processing_times)
            max_time = max(processing_times)
            
            # 计算百分位数
            sorted_times = sorted(processing_times)
            p50 = sorted_times[len(sorted_times) // 2]
            p95 = sorted_times[int(len(sorted_times) * 0.95)]
            
            # 识别性能问题
            slow_responses = [t for t in processing_times if t > 5.0]
            very_slow_responses = [t for t in processing_times if t > 10.0]
            
            return {
                "avg_response_time": avg_time,
                "min_response_time": min_time,
                "max_response_time": max_time,
                "p50_response_time": p50,
                "p95_response_time": p95,
                "total_messages": len(messages),
                "slow_responses_count": len(slow_responses),
                "very_slow_responses_count": len(very_slow_responses),
                "performance_issues": self._identify_performance_issues(processing_times)
            }
            
        except Exception as e:
            logger.error(f"分析响应时间失败: {e}")
            return {}
            
    def _identify_performance_issues(self, processing_times: List[float]) -> List[str]:
        """识别性能问题"""
        try:
            issues = []
            
            avg_time = sum(processing_times) / len(processing_times)
            
            if avg_time > 3.0:
                issues.append("平均响应时间过长")
                
            slow_count = len([t for t in processing_times if t > 5.0])
            if slow_count > len(processing_times) * 0.1:
                issues.append("存在较多慢响应")
                
            very_slow_count = len([t for t in processing_times if t > 10.0])
            if very_slow_count > 0:
                issues.append("存在极慢响应")
                
            return issues
            
        except Exception as e:
            logger.error(f"识别性能问题失败: {e}")
            return []
            
    async def _analyze_customer_satisfaction(
        self,
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: datetime
    ) -> Dict[str, Any]:
        """分析客户满意度"""
        try:
            # 查询有满意度评分的会话
            sessions_query = select(CustomerServiceSession).where(
                and_(
                    CustomerServiceSession.project_id == project_id,
                    CustomerServiceSession.started_at >= start_date,
                    CustomerServiceSession.satisfaction_score.isnot(None)
                )
            )
            
            sessions_result = await db.execute(sessions_query)
            sessions = sessions_result.scalars().all()
            
            if not sessions:
                return {"avg_satisfaction": 0, "total_rated": 0}
            
            scores = [s.satisfaction_score for s in sessions]
            avg_satisfaction = sum(scores) / len(scores)
            
            # 分析满意度分布
            excellent = len([s for s in scores if s >= 4.5])
            good = len([s for s in scores if 3.5 <= s < 4.5])
            average = len([s for s in scores if 2.5 <= s < 3.5])
            poor = len([s for s in scores if s < 2.5])
            
            return {
                "avg_satisfaction": avg_satisfaction,
                "total_rated": len(sessions),
                "distribution": {
                    "excellent": excellent,
                    "good": good,
                    "average": average,
                    "poor": poor
                },
                "satisfaction_trend": "stable",  # 可以添加趋势分析
                "improvement_suggestions": self._get_satisfaction_improvements(avg_satisfaction, poor)
            }
            
        except Exception as e:
            logger.error(f"分析客户满意度失败: {e}")
            return {}
            
    def _get_satisfaction_improvements(self, avg_score: float, poor_count: int) -> List[str]:
        """获取满意度改进建议"""
        suggestions = []
        
        if avg_score < 3.5:
            suggestions.append("整体满意度偏低，需要全面改进服务质量")
            
        if poor_count > 0:
            suggestions.append("存在低满意度评价，需要分析具体原因")
            
        if avg_score < 4.0:
            suggestions.append("可以通过提高回复准确性来改善满意度")
            
        return suggestions
        
    async def _generate_optimization_suggestions(
        self,
        quality_analysis: Dict[str, Any],
        intent_analysis: Dict[str, Any],
        response_time_analysis: Dict[str, Any],
        satisfaction_analysis: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成优化建议"""
        try:
            suggestions = []
            
            # 基于质量分析的建议
            if quality_analysis.get("avg_quality", 0) < 0.7:
                suggestions.append({
                    "type": "quality_improvement",
                    "priority": "high",
                    "title": "提升回复质量",
                    "description": "AI回复质量偏低，建议优化提示词和训练数据",
                    "actions": [
                        "分析低质量回复模式",
                        "优化AI提示词",
                        "增加训练样本",
                        "改进知识库内容"
                    ]
                })
            
            # 基于响应时间的建议
            if response_time_analysis.get("avg_response_time", 0) > 3.0:
                suggestions.append({
                    "type": "performance_optimization",
                    "priority": "medium",
                    "title": "优化响应速度",
                    "description": "响应时间过长，需要优化系统性能",
                    "actions": [
                        "优化AI模型推理速度",
                        "增加缓存机制",
                        "优化知识库检索",
                        "考虑使用更快的模型"
                    ]
                })
            
            # 基于满意度的建议
            if satisfaction_analysis.get("avg_satisfaction", 0) < 4.0:
                suggestions.append({
                    "type": "satisfaction_improvement",
                    "priority": "high",
                    "title": "提升客户满意度",
                    "description": "客户满意度有待提升",
                    "actions": [
                        "分析低满意度对话",
                        "改进回复的个性化程度",
                        "增强情感理解能力",
                        "优化业务流转效率"
                    ]
                })
            
            # 基于意图识别的建议
            if intent_analysis.get("accuracy_score", 0) < 0.8:
                suggestions.append({
                    "type": "intent_optimization",
                    "priority": "medium",
                    "title": "改进意图识别",
                    "description": "意图识别准确性需要提升",
                    "actions": [
                        "扩充意图训练数据",
                        "优化意图分类模型",
                        "增加上下文理解",
                        "改进关键词匹配规则"
                    ]
                })
            
            return suggestions
            
        except Exception as e:
            logger.error(f"生成优化建议失败: {e}")
            return []
            
    def _calculate_overall_score(
        self,
        quality_analysis: Dict[str, Any],
        intent_analysis: Dict[str, Any],
        satisfaction_analysis: Dict[str, Any]
    ) -> float:
        """计算综合评分"""
        try:
            quality_score = quality_analysis.get("avg_quality", 0) * 0.3
            intent_score = intent_analysis.get("accuracy_score", 0) * 0.3
            satisfaction_score = (satisfaction_analysis.get("avg_satisfaction", 0) / 5.0) * 0.4
            
            return quality_score + intent_score + satisfaction_score
            
        except Exception as e:
            logger.error(f"计算综合评分失败: {e}")
            return 0.0
            
    def _get_quality_distribution(self, quality_scores: List[float]) -> Dict[str, int]:
        """获取质量分布"""
        try:
            excellent = len([s for s in quality_scores if s >= 0.8])
            good = len([s for s in quality_scores if 0.6 <= s < 0.8])
            average = len([s for s in quality_scores if 0.4 <= s < 0.6])
            poor = len([s for s in quality_scores if s < 0.4])
            
            return {
                "excellent": excellent,
                "good": good,
                "average": average,
                "poor": poor
            }
            
        except Exception as e:
            logger.error(f"获取质量分布失败: {e}")
            return {}
            
    async def apply_optimizations(
        self,
        db: AsyncSession,
        project_id: uuid.UUID,
        optimization_suggestions: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """应用优化建议"""
        try:
            applied_optimizations = []
            
            for suggestion in optimization_suggestions:
                if suggestion.get("priority") == "high":
                    # 自动应用高优先级优化
                    result = await self._apply_single_optimization(
                        db, project_id, suggestion
                    )
                    applied_optimizations.append(result)
            
            return {
                "applied_count": len(applied_optimizations),
                "optimizations": applied_optimizations
            }
            
        except Exception as e:
            logger.error(f"应用优化失败: {e}")
            return {}
            
    async def _apply_single_optimization(
        self,
        db: AsyncSession,
        project_id: uuid.UUID,
        suggestion: Dict[str, Any]
    ) -> Dict[str, Any]:
        """应用单个优化建议"""
        try:
            optimization_type = suggestion.get("type")
            
            if optimization_type == "quality_improvement":
                # 应用质量改进
                return await self._apply_quality_optimization(db, project_id, suggestion)
            elif optimization_type == "performance_optimization":
                # 应用性能优化
                return await self._apply_performance_optimization(db, project_id, suggestion)
            else:
                # 记录建议但不自动应用
                return {
                    "type": optimization_type,
                    "status": "recorded",
                    "message": "建议已记录，需要手动处理"
                }
                
        except Exception as e:
            logger.error(f"应用单个优化失败: {e}")
            return {"status": "failed", "error": str(e)}
            
    async def _apply_quality_optimization(
        self,
        db: AsyncSession,
        project_id: uuid.UUID,
        suggestion: Dict[str, Any]
    ) -> Dict[str, Any]:
        """应用质量优化"""
        try:
            # 这里可以实现自动的质量优化
            # 例如：更新提示词、调整模型参数等
            
            return {
                "type": "quality_improvement",
                "status": "applied",
                "message": "质量优化已应用",
                "details": suggestion.get("actions", [])
            }
            
        except Exception as e:
            logger.error(f"应用质量优化失败: {e}")
            return {"status": "failed", "error": str(e)}
            
    async def _apply_performance_optimization(
        self,
        db: AsyncSession,
        project_id: uuid.UUID,
        suggestion: Dict[str, Any]
    ) -> Dict[str, Any]:
        """应用性能优化"""
        try:
            # 这里可以实现自动的性能优化
            # 例如：调整缓存策略、优化查询等
            
            return {
                "type": "performance_optimization",
                "status": "applied",
                "message": "性能优化已应用",
                "details": suggestion.get("actions", [])
            }
            
        except Exception as e:
            logger.error(f"应用性能优化失败: {e}")
            return {"status": "failed", "error": str(e)}
