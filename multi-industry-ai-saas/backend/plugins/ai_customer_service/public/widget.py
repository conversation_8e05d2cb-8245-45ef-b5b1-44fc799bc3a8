#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 智能体客服公共组件 API

提供公共访问的客服组件，支持嵌入到任何网站。
"""

import logging
import uuid
import json
from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, Request, HTTPException, status, Query, Path
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 模板引擎
templates = Jinja2Templates(directory="plugins/ai_customer_service/templates")

# 请求模型
class PublicChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    user_info: Optional[Dict[str, Any]] = None
    widget_config: Optional[Dict[str, Any]] = None

class WidgetConfigRequest(BaseModel):
    title: str = "AI智能客服"
    welcome_message: str = "您好！我是AI智能客服，有什么可以帮助您的吗？"
    theme_color: str = "#1890ff"
    position: str = "bottom-right"  # bottom-right, bottom-left, top-right, top-left
    size: str = "medium"  # small, medium, large
    auto_open: bool = False
    show_avatar: bool = True
    enable_file_upload: bool = False
    enable_voice_input: bool = False
    custom_css: Optional[str] = None

@router.get(
    "/widget/{widget_id}",
    response_class=HTMLResponse,
    summary="获取客服组件页面",
    description="获取可嵌入网站的客服组件页面"
)
async def get_widget_page(
    request: Request,
    widget_id: str = Path(..., description="组件ID"),
    theme: str = Query("light", description="主题 (light/dark)"),
    size: str = Query("medium", description="尺寸 (small/medium/large)"),
    position: str = Query("bottom-right", description="位置"),
    auto_open: bool = Query(False, description="是否自动打开")
):
    """获取客服组件页面"""
    try:
        # 这里需要根据widget_id获取配置
        # 暂时使用默认配置
        
        widget_config = {
            "widget_id": widget_id,
            "title": "AI智能客服",
            "welcome_message": "您好！我是AI智能客服，有什么可以帮助您的吗？",
            "theme": theme,
            "theme_color": "#1890ff",
            "position": position,
            "size": size,
            "auto_open": auto_open,
            "show_avatar": True,
            "enable_file_upload": False,
            "enable_voice_input": False,
            "api_base_url": str(request.base_url).rstrip("/"),
            "websocket_url": str(request.base_url).replace("http", "ws").rstrip("/")
        }
        
        return templates.TemplateResponse(
            "widget.html",
            {
                "request": request,
                "widget_config": widget_config,
                "config_json": json.dumps(widget_config)
            }
        )
        
    except Exception as e:
        logger.error(f"获取组件页面失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取组件页面失败"
        )

@router.get(
    "/embed/{widget_id}",
    response_class=HTMLResponse,
    summary="获取嵌入代码",
    description="获取可嵌入网站的JavaScript代码"
)
async def get_embed_code(
    request: Request,
    widget_id: str = Path(..., description="组件ID"),
    theme: str = Query("light", description="主题"),
    size: str = Query("medium", description="尺寸"),
    position: str = Query("bottom-right", description="位置")
):
    """获取嵌入代码"""
    try:
        base_url = str(request.base_url).rstrip("/")
        widget_url = f"{base_url}/api/v1/public/ai-customer-service/widget/{widget_id}"
        
        embed_code = f"""
<!-- AI智能客服组件 -->
<script>
(function() {{
    var config = {{
        widgetId: '{widget_id}',
        theme: '{theme}',
        size: '{size}',
        position: '{position}',
        apiBaseUrl: '{base_url}'
    }};
    
    var script = document.createElement('script');
    script.src = '{base_url}/static/ai-customer-service/widget.js';
    script.onload = function() {{
        if (window.AICustomerService) {{
            window.AICustomerService.init(config);
        }}
    }};
    document.head.appendChild(script);
    
    var link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '{base_url}/static/ai-customer-service/widget.css';
    document.head.appendChild(link);
}})();
</script>
"""
        
        return HTMLResponse(content=embed_code, media_type="text/html")
        
    except Exception as e:
        logger.error(f"获取嵌入代码失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取嵌入代码失败"
        )

@router.post(
    "/chat/{widget_id}",
    summary="公共聊天接口",
    description="处理公共客服组件的聊天请求"
)
async def public_chat(
    widget_id: str = Path(..., description="组件ID"),
    chat_request: PublicChatRequest = ...
):
    """处理公共聊天请求"""
    try:
        # 生成或获取会话ID
        session_id = chat_request.session_id or f"public_{uuid.uuid4().hex[:12]}"
        
        # 这里需要调用客服管理器处理消息
        # 暂时返回模拟响应
        
        ai_response = {
            "message": f"您好！我收到了您的消息：{chat_request.message}。这是一个模拟回复。",
            "session_id": session_id,
            "timestamp": datetime.utcnow().isoformat(),
            "suggestions": [
                "我想了解产品信息",
                "我需要技术支持",
                "我要投诉建议"
            ]
        }
        
        return JSONResponse(content={
            "success": True,
            "data": ai_response
        })
        
    except Exception as e:
        logger.error(f"处理公共聊天失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="处理聊天失败"
        )

@router.get(
    "/config/{widget_id}",
    summary="获取组件配置",
    description="获取指定组件的配置信息"
)
async def get_widget_config(
    widget_id: str = Path(..., description="组件ID")
):
    """获取组件配置"""
    try:
        # 这里需要从数据库获取实际配置
        # 暂时返回默认配置
        
        config = {
            "widget_id": widget_id,
            "title": "AI智能客服",
            "welcome_message": "您好！我是AI智能客服，有什么可以帮助您的吗？",
            "theme_color": "#1890ff",
            "position": "bottom-right",
            "size": "medium",
            "auto_open": False,
            "show_avatar": True,
            "enable_file_upload": False,
            "enable_voice_input": False,
            "business_hours": {
                "enabled": False,
                "timezone": "Asia/Shanghai",
                "schedule": {
                    "monday": {"start": "09:00", "end": "18:00"},
                    "tuesday": {"start": "09:00", "end": "18:00"},
                    "wednesday": {"start": "09:00", "end": "18:00"},
                    "thursday": {"start": "09:00", "end": "18:00"},
                    "friday": {"start": "09:00", "end": "18:00"},
                    "saturday": {"start": "10:00", "end": "16:00"},
                    "sunday": {"start": "10:00", "end": "16:00"}
                }
            },
            "offline_message": "客服暂时离线，请留言，我们会尽快回复您。",
            "quick_replies": [
                "产品咨询",
                "技术支持", 
                "价格信息",
                "联系方式"
            ]
        }
        
        return JSONResponse(content={
            "success": True,
            "data": config
        })
        
    except Exception as e:
        logger.error(f"获取组件配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取配置失败"
        )

@router.post(
    "/config/{widget_id}",
    summary="更新组件配置",
    description="更新指定组件的配置"
)
async def update_widget_config(
    widget_id: str = Path(..., description="组件ID"),
    config_request: WidgetConfigRequest = ...
):
    """更新组件配置"""
    try:
        # 这里需要保存配置到数据库
        # 暂时返回成功响应
        
        updated_config = {
            "widget_id": widget_id,
            "title": config_request.title,
            "welcome_message": config_request.welcome_message,
            "theme_color": config_request.theme_color,
            "position": config_request.position,
            "size": config_request.size,
            "auto_open": config_request.auto_open,
            "show_avatar": config_request.show_avatar,
            "enable_file_upload": config_request.enable_file_upload,
            "enable_voice_input": config_request.enable_voice_input,
            "custom_css": config_request.custom_css,
            "updated_at": datetime.utcnow().isoformat()
        }
        
        return JSONResponse(content={
            "success": True,
            "message": "配置更新成功",
            "data": updated_config
        })
        
    except Exception as e:
        logger.error(f"更新组件配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新配置失败"
        )

@router.get(
    "/analytics/{widget_id}",
    summary="获取组件分析数据",
    description="获取指定组件的使用分析数据"
)
async def get_widget_analytics(
    widget_id: str = Path(..., description="组件ID"),
    days: int = Query(7, ge=1, le=90, description="分析天数")
):
    """获取组件分析数据"""
    try:
        # 这里需要从数据库获取实际分析数据
        # 暂时返回模拟数据
        
        analytics = {
            "widget_id": widget_id,
            "period_days": days,
            "total_sessions": 156,
            "total_messages": 892,
            "avg_session_duration": 180,  # 秒
            "user_satisfaction": 4.2,
            "top_intents": [
                {"intent": "product_inquiry", "count": 45, "percentage": 28.8},
                {"intent": "technical_support", "count": 32, "percentage": 20.5},
                {"intent": "pricing", "count": 28, "percentage": 17.9},
                {"intent": "contact_info", "count": 25, "percentage": 16.0},
                {"intent": "complaint", "count": 15, "percentage": 9.6}
            ],
            "daily_stats": [
                {"date": "2025-01-01", "sessions": 22, "messages": 128},
                {"date": "2025-01-02", "sessions": 18, "messages": 95},
                {"date": "2025-01-03", "sessions": 25, "messages": 142},
                {"date": "2025-01-04", "sessions": 20, "messages": 115},
                {"date": "2025-01-05", "sessions": 28, "messages": 165},
                {"date": "2025-01-06", "sessions": 24, "messages": 138},
                {"date": "2025-01-07", "sessions": 19, "messages": 109}
            ],
            "device_stats": {
                "desktop": 45.2,
                "mobile": 42.3,
                "tablet": 12.5
            },
            "browser_stats": {
                "chrome": 58.7,
                "safari": 22.1,
                "firefox": 12.8,
                "edge": 6.4
            }
        }
        
        return JSONResponse(content={
            "success": True,
            "data": analytics
        })
        
    except Exception as e:
        logger.error(f"获取组件分析数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取分析数据失败"
        )
