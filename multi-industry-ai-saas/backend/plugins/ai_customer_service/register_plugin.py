#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 智能体客服插件注册脚本

用于将插件注册到插件市场，支持安装和卸载。
"""

import asyncio
import uuid
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_async_engine
from models.plugin import Plugin, PluginVersion
from . import plugin_info

async def register_plugin_to_market():
    """将插件注册到插件市场"""
    try:
        engine = get_async_engine()
        
        async with engine.begin() as conn:
            # 创建会话
            from sqlalchemy.ext.asyncio import async_sessionmaker
            async_session = async_sessionmaker(engine, expire_on_commit=False)
            
            async with async_session() as db:
                # 检查插件是否已存在
                from sqlalchemy import select
                existing_plugin = await db.execute(
                    select(Plugin).where(Plugin.code == plugin_info["code"])
                )
                plugin = existing_plugin.scalar_one_or_none()
                
                if plugin:
                    # 更新现有插件
                    plugin.name = plugin_info["name"]
                    plugin.description = plugin_info["description"]
                    plugin.version = plugin_info["version"]
                    plugin.author = plugin_info["author"]
                    plugin.category = plugin_info["category"]
                    plugin.price = plugin_info["price"]
                    plugin.billing_cycle = plugin_info["billing_cycle"]
                    plugin.is_system = plugin_info["is_system"]
                    plugin.is_active = plugin_info["is_active"]
                    plugin.requires_subscription = plugin_info["requires_subscription"]
                    plugin.installation_path = "plugins.ai_customer_service"
                    plugin.entry_point = "initialize_plugin"
                    plugin.settings_schema = plugin_info["config_schema"]
                    plugin.features = plugin_info["features"]
                    plugin.updated_at = datetime.utcnow()
                    
                    print(f"更新插件: {plugin_info['name']}")
                    
                else:
                    # 创建新插件
                    plugin = Plugin(
                        id=uuid.uuid4(),
                        code=plugin_info["code"],
                        name=plugin_info["name"],
                        description=plugin_info["description"],
                        version=plugin_info["version"],
                        author=plugin_info["author"],
                        category=plugin_info["category"],
                        price=plugin_info["price"],
                        billing_cycle=plugin_info["billing_cycle"],
                        is_system=plugin_info["is_system"],
                        is_active=plugin_info["is_active"],
                        requires_subscription=plugin_info["requires_subscription"],
                        installation_path="plugins.ai_customer_service",
                        entry_point="initialize_plugin",
                        settings_schema=plugin_info["config_schema"],
                        features=plugin_info["features"]
                    )
                    
                    db.add(plugin)
                    print(f"注册新插件: {plugin_info['name']}")
                
                # 创建或更新插件版本
                existing_version = await db.execute(
                    select(PluginVersion).where(
                        PluginVersion.plugin_id == plugin.id,
                        PluginVersion.version == plugin_info["version"]
                    )
                )
                version = existing_version.scalar_one_or_none()
                
                if not version:
                    # 将之前的版本标记为非最新
                    await db.execute(
                        f"UPDATE plugin_versions SET is_latest = false WHERE plugin_id = '{plugin.id}'"
                    )
                    
                    # 创建新版本
                    version = PluginVersion(
                        id=uuid.uuid4(),
                        plugin_id=plugin.id,
                        version=plugin_info["version"],
                        release_notes=f"AI 智能体客服插件 v{plugin_info['version']} 发布",
                        is_compatible=True,
                        is_latest=True,
                        released_at=datetime.utcnow()
                    )
                    
                    db.add(version)
                    print(f"创建插件版本: {plugin_info['version']}")
                
                await db.commit()
                print("插件注册完成！")
                
                return plugin.id
                
    except Exception as e:
        print(f"注册插件失败: {e}")
        raise

async def unregister_plugin_from_market():
    """从插件市场注销插件"""
    try:
        engine = get_async_engine()
        
        async with engine.begin() as conn:
            from sqlalchemy.ext.asyncio import async_sessionmaker
            async_session = async_sessionmaker(engine, expire_on_commit=False)
            
            async with async_session() as db:
                # 查找插件
                from sqlalchemy import select
                existing_plugin = await db.execute(
                    select(Plugin).where(Plugin.code == plugin_info["code"])
                )
                plugin = existing_plugin.scalar_one_or_none()
                
                if plugin:
                    # 标记为不活跃而不是删除
                    plugin.is_active = False
                    plugin.updated_at = datetime.utcnow()
                    
                    await db.commit()
                    print(f"插件已注销: {plugin_info['name']}")
                else:
                    print("插件不存在，无需注销")
                    
    except Exception as e:
        print(f"注销插件失败: {e}")
        raise

async def check_plugin_status():
    """检查插件状态"""
    try:
        engine = get_async_engine()
        
        async with engine.begin() as conn:
            from sqlalchemy.ext.asyncio import async_sessionmaker
            async_session = async_sessionmaker(engine, expire_on_commit=False)
            
            async with async_session() as db:
                from sqlalchemy import select
                existing_plugin = await db.execute(
                    select(Plugin).where(Plugin.code == plugin_info["code"])
                )
                plugin = existing_plugin.scalar_one_or_none()
                
                if plugin:
                    print(f"插件状态:")
                    print(f"  ID: {plugin.id}")
                    print(f"  名称: {plugin.name}")
                    print(f"  版本: {plugin.version}")
                    print(f"  状态: {'活跃' if plugin.is_active else '不活跃'}")
                    print(f"  系统插件: {'是' if plugin.is_system else '否'}")
                    print(f"  创建时间: {plugin.created_at}")
                    print(f"  更新时间: {plugin.updated_at}")
                    
                    # 查询版本信息
                    versions = await db.execute(
                        select(PluginVersion).where(PluginVersion.plugin_id == plugin.id)
                    )
                    version_list = versions.scalars().all()
                    
                    print(f"  版本历史:")
                    for v in version_list:
                        status = "最新" if v.is_latest else "历史"
                        print(f"    - {v.version} ({status})")
                        
                else:
                    print("插件未注册")
                    
    except Exception as e:
        print(f"检查插件状态失败: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("用法:")
        print("  python register_plugin.py register   # 注册插件")
        print("  python register_plugin.py unregister # 注销插件")
        print("  python register_plugin.py status     # 检查状态")
        sys.exit(1)
    
    action = sys.argv[1]
    
    if action == "register":
        asyncio.run(register_plugin_to_market())
    elif action == "unregister":
        asyncio.run(unregister_plugin_from_market())
    elif action == "status":
        asyncio.run(check_plugin_status())
    else:
        print(f"未知操作: {action}")
        sys.exit(1)
