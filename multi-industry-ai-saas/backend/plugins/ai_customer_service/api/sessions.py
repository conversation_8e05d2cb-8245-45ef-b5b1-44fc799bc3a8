#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
客服会话 API
"""

import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from api.deps import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User
from models.project import Project
from ..services.customer_service import CustomerServiceManager

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 请求模型
class SessionCreateRequest(BaseModel):
    platform_type: str
    platform_user_id: str
    user_id: Optional[str] = None
    assistant_id: Optional[str] = None
    title: Optional[str] = None

class SessionUpdateRequest(BaseModel):
    status: Optional[str] = None
    title: Optional[str] = None
    satisfaction_score: Optional[float] = None

class ManualMessageRequest(BaseModel):
    content: str
    message_type: str = "text"
    attachments: Optional[List[Dict[str, Any]]] = None

# 响应模型
class SessionResponse(BaseModel):
    id: str
    session_id: str
    platform_type: str
    platform_user_id: str
    status: str
    title: Optional[str]
    message_count: int
    satisfaction_score: Optional[float]
    started_at: datetime
    last_activity_at: datetime
    ended_at: Optional[datetime]

class SessionListResponse(BaseModel):
    items: List[SessionResponse]
    total: int
    page: int
    size: int
    pages: int

# 获取客服管理器实例
def get_customer_service_manager() -> CustomerServiceManager:
    # 这里应该从插件系统获取实例
    # 暂时创建新实例
    return CustomerServiceManager()

@router.get(
    "",
    response_model=SessionListResponse,
    summary="获取客服会话列表",
    description="获取项目下的客服会话列表，支持分页和过滤"
)
async def get_sessions(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    status: Optional[str] = Query(None, description="会话状态过滤"),
    platform_type: Optional[str] = Query(None, description="平台类型过滤"),
    platform_user_id: Optional[str] = Query(None, description="平台用户ID过滤"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取客服会话列表"""
    try:
        # 这里需要实现实际的数据库查询
        # 暂时返回模拟数据
        
        sessions = []
        for i in range(min(size, 10)):  # 模拟数据
            sessions.append(SessionResponse(
                id=str(uuid.uuid4()),
                session_id=f"session_{i}",
                platform_type="wechat",
                platform_user_id=f"user_{i}",
                status="active",
                title=f"客服会话 {i}",
                message_count=5 + i,
                satisfaction_score=4.0 + (i % 5) * 0.2,
                started_at=datetime.utcnow() - timedelta(hours=i),
                last_activity_at=datetime.utcnow() - timedelta(minutes=i * 10),
                ended_at=None
            ))
        
        total = 50  # 模拟总数
        pages = (total + size - 1) // size
        
        return {
            "items": sessions,
            "total": total,
            "page": page,
            "size": size,
            "pages": pages
        }
        
    except Exception as e:
        logger.error(f"获取会话列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会话列表失败: {str(e)}"
        )

@router.get(
    "/{session_id}",
    response_model=SessionResponse,
    summary="获取会话详情",
    description="获取指定会话的详细信息"
)
async def get_session(
    session_id: str = Path(..., description="会话ID"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取会话详情"""
    try:
        # 这里需要实现实际的数据库查询
        # 暂时返回模拟数据
        
        return SessionResponse(
            id=str(uuid.uuid4()),
            session_id=session_id,
            platform_type="wechat",
            platform_user_id="user_123",
            status="active",
            title="客服会话详情",
            message_count=10,
            satisfaction_score=4.5,
            started_at=datetime.utcnow() - timedelta(hours=2),
            last_activity_at=datetime.utcnow() - timedelta(minutes=5),
            ended_at=None
        )
        
    except Exception as e:
        logger.error(f"获取会话详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会话详情失败: {str(e)}"
        )

@router.post(
    "",
    response_model=SessionResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建客服会话",
    description="创建新的客服会话"
)
async def create_session(
    session_data: SessionCreateRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """创建客服会话"""
    try:
        # 这里需要实现实际的会话创建逻辑
        # 暂时返回模拟数据
        
        new_session = SessionResponse(
            id=str(uuid.uuid4()),
            session_id=f"session_{uuid.uuid4().hex[:8]}",
            platform_type=session_data.platform_type,
            platform_user_id=session_data.platform_user_id,
            status="active",
            title=session_data.title or "新客服会话",
            message_count=0,
            satisfaction_score=None,
            started_at=datetime.utcnow(),
            last_activity_at=datetime.utcnow(),
            ended_at=None
        )
        
        return new_session
        
    except Exception as e:
        logger.error(f"创建会话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建会话失败: {str(e)}"
        )

@router.put(
    "/{session_id}",
    response_model=SessionResponse,
    summary="更新会话信息",
    description="更新指定会话的信息"
)
async def update_session(
    session_id: str = Path(..., description="会话ID"),
    session_data: SessionUpdateRequest = ...,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """更新会话信息"""
    try:
        # 这里需要实现实际的会话更新逻辑
        # 暂时返回模拟数据
        
        return SessionResponse(
            id=str(uuid.uuid4()),
            session_id=session_id,
            platform_type="wechat",
            platform_user_id="user_123",
            status=session_data.status or "active",
            title=session_data.title or "更新的会话",
            message_count=10,
            satisfaction_score=session_data.satisfaction_score,
            started_at=datetime.utcnow() - timedelta(hours=2),
            last_activity_at=datetime.utcnow(),
            ended_at=datetime.utcnow() if session_data.status == "closed" else None
        )
        
    except Exception as e:
        logger.error(f"更新会话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新会话失败: {str(e)}"
        )

@router.post(
    "/{session_id}/messages",
    summary="发送手动消息",
    description="向指定会话发送手动消息"
)
async def send_manual_message(
    session_id: str = Path(..., description="会话ID"),
    message_data: ManualMessageRequest = ...,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    cs_manager: CustomerServiceManager = Depends(get_customer_service_manager)
):
    """发送手动消息"""
    try:
        # 这里需要获取会话信息以确定平台类型和用户ID
        # 暂时使用模拟数据
        platform_type = "wechat"
        platform_user_id = "user_123"
        
        success = await cs_manager.send_manual_message(
            platform_type=platform_type,
            platform_user_id=platform_user_id,
            message=message_data.content,
            message_type=message_data.message_type,
            attachments=message_data.attachments
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="发送消息失败"
            )
        
        return {"success": True, "message": "消息发送成功"}
        
    except Exception as e:
        logger.error(f"发送手动消息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发送手动消息失败: {str(e)}"
        )

@router.delete(
    "/{session_id}",
    summary="关闭会话",
    description="关闭指定的客服会话"
)
async def close_session(
    session_id: str = Path(..., description="会话ID"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    cs_manager: CustomerServiceManager = Depends(get_customer_service_manager)
):
    """关闭会话"""
    try:
        success = await cs_manager.close_session(session_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会话不存在或已关闭"
            )
        
        return {"success": True, "message": "会话已关闭"}
        
    except Exception as e:
        logger.error(f"关闭会话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"关闭会话失败: {str(e)}"
        )

@router.get(
    "/{session_id}/messages",
    summary="获取会话消息",
    description="获取指定会话的消息列表"
)
async def get_session_messages(
    session_id: str = Path(..., description="会话ID"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取会话消息"""
    try:
        # 这里需要实现实际的数据库查询
        # 暂时返回模拟数据

        messages = []
        for i in range(5):  # 模拟5条消息
            messages.append({
                "id": str(uuid.uuid4()),
                "content": f"这是第{i+1}条消息",
                "is_from_user": i % 2 == 0,
                "sender_name": "用户" if i % 2 == 0 else "AI助手",
                "ai_model_used": None if i % 2 == 0 else "gpt-4",
                "confidence_score": None if i % 2 == 0 else 0.95,
                "created_at": (datetime.utcnow() - timedelta(minutes=i*5)).isoformat()
            })

        return {
            "messages": messages
        }

    except Exception as e:
        logger.error(f"获取会话消息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会话消息失败: {str(e)}"
        )

@router.get(
    "/active/list",
    summary="获取活跃会话",
    description="获取当前活跃的客服会话列表"
)
async def get_active_sessions(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    cs_manager: CustomerServiceManager = Depends(get_customer_service_manager)
):
    """获取活跃会话"""
    try:
        active_sessions = await cs_manager.get_active_sessions()

        return {
            "success": True,
            "data": active_sessions,
            "total": len(active_sessions)
        }

    except Exception as e:
        logger.error(f"获取活跃会话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取活跃会话失败: {str(e)}"
        )
