#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI客服对话 API
"""

import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, Form
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from api.deps import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User
from models.project import Project

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 请求模型
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    platform_type: str = "test"
    platform_user_id: str = "test_user"
    image_urls: Optional[List[str]] = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    model_used: str
    confidence: float
    response_time: int
    has_image_analysis: bool = False
    image_analysis_results: Optional[List[Dict[str, Any]]] = None

@router.post(
    "/chat",
    response_model=ChatResponse,
    summary="AI客服对话",
    description="与AI客服进行对话，支持文本和图片"
)
async def chat_with_ai(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """AI客服对话"""
    try:
        start_time = datetime.utcnow()
        
        # 获取项目的AI助手配置
        ai_assistant_response = await get_project_ai_assistant(project.id, db)
        
        # 准备对话请求
        chat_payload = {
            "message": request.message,
            "session_id": request.session_id or str(uuid.uuid4()),
            "user_id": request.platform_user_id,
            "context": {
                "platform": request.platform_type,
                "project_id": str(project.id)
            }
        }
        
        # 如果有图片，添加视觉分析
        image_analysis_results = []
        has_image_analysis = False
        
        if request.image_urls:
            has_image_analysis = True
            for image_url in request.image_urls:
                try:
                    # 调用视觉模型分析图片
                    vision_result = await analyze_image_with_vision_model(
                        image_url, project.id, db
                    )
                    image_analysis_results.append(vision_result)
                    
                    # 将图片分析结果添加到对话上下文
                    chat_payload["context"]["image_analysis"] = image_analysis_results
                    
                except Exception as e:
                    logger.error(f"图片分析失败: {e}")
                    image_analysis_results.append({
                        "image_url": image_url,
                        "error": "图片分析失败",
                        "analysis": "无法分析此图片"
                    })
        
        # 调用AI助手进行对话
        ai_response = await call_ai_assistant_chat(ai_assistant_response, chat_payload, db)
        
        # 计算响应时间
        end_time = datetime.utcnow()
        response_time = int((end_time - start_time).total_seconds() * 1000)
        
        return ChatResponse(
            response=ai_response.get("response", "抱歉，我暂时无法回答这个问题。"),
            session_id=chat_payload["session_id"],
            model_used=ai_response.get("model_used", "AI助手"),
            confidence=ai_response.get("confidence", 0.8),
            response_time=response_time,
            has_image_analysis=has_image_analysis,
            image_analysis_results=image_analysis_results if has_image_analysis else None
        )
        
    except Exception as e:
        logger.error(f"AI对话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI对话失败: {str(e)}"
        )

@router.post(
    "/upload-image",
    summary="上传图片",
    description="上传图片用于AI分析"
)
async def upload_image(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project)
):
    """上传图片"""
    try:
        # 验证文件类型
        if not file.content_type.startswith('image/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只支持图片文件"
            )
        
        # 生成文件名
        file_extension = file.filename.split('.')[-1] if '.' in file.filename else 'jpg'
        filename = f"chat_image_{uuid.uuid4().hex}.{file_extension}"
        
        # 这里应该保存到文件存储系统（如OSS、S3等）
        # 暂时返回一个模拟的URL
        image_url = f"/api/v1/files/images/{filename}"
        
        return {
            "success": True,
            "image_url": image_url,
            "filename": filename
        }
        
    except Exception as e:
        logger.error(f"图片上传失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"图片上传失败: {str(e)}"
        )

async def get_project_ai_assistant(project_id: uuid.UUID, db: AsyncSession) -> Dict[str, Any]:
    """获取项目的AI助手配置"""
    try:
        # 这里应该查询项目的AI助手配置
        # 暂时返回默认配置
        return {
            "assistant_id": "default",
            "model_id": "gpt-4",
            "temperature": 0.7,
            "max_tokens": 2000,
            "system_prompt": "你是一个专业的AI客服助手，请友好、准确地回答用户的问题。"
        }
    except Exception as e:
        logger.error(f"获取AI助手配置失败: {e}")
        return {
            "assistant_id": "default",
            "model_id": "gpt-4",
            "temperature": 0.7,
            "max_tokens": 2000,
            "system_prompt": "你是一个专业的AI客服助手。"
        }

async def analyze_image_with_vision_model(
    image_url: str, 
    project_id: uuid.UUID, 
    db: AsyncSession
) -> Dict[str, Any]:
    """使用视觉模型分析图片"""
    try:
        # 这里应该调用项目配置的视觉模型
        # 暂时返回模拟的分析结果
        return {
            "image_url": image_url,
            "analysis": "这是一张图片，包含了相关的视觉内容。",
            "objects": ["图片内容"],
            "text": "",
            "confidence": 0.85
        }
    except Exception as e:
        logger.error(f"视觉模型分析失败: {e}")
        return {
            "image_url": image_url,
            "error": str(e),
            "analysis": "图片分析失败"
        }

async def call_ai_assistant_chat(
    assistant_config: Dict[str, Any], 
    chat_payload: Dict[str, Any], 
    db: AsyncSession
) -> Dict[str, Any]:
    """调用AI助手进行对话"""
    try:
        # 这里应该调用实际的AI助手API
        # 暂时返回模拟的响应
        
        message = chat_payload["message"]
        image_context = ""
        
        # 如果有图片分析结果，添加到上下文
        if "image_analysis" in chat_payload.get("context", {}):
            analyses = chat_payload["context"]["image_analysis"]
            image_context = "用户上传了图片，分析结果：" + "; ".join([
                analysis.get("analysis", "") for analysis in analyses
            ])
        
        # 调用真实的AI助手API
        try:
            # 导入AI助手服务
            from services.ai_assistant import AIAssistantService

            # 获取AI助手服务实例
            ai_service = AIAssistantService()

            # 构建聊天请求
            chat_request = {
                "message": message,
                "session_id": session_id,
                "image_urls": image_urls if image_urls else [],
                "context": {
                    "platform_type": platform_type,
                    "platform_user_id": platform_user_id,
                    "project_id": str(project.id)
                }
            }

            # 调用AI助手
            ai_response = await ai_service.chat(
                project_id=project.id,
                db=db,
                **chat_request
            )

            return {
                "response": ai_response.get("response", "抱歉，我暂时无法回答这个问题。"),
                "model_used": ai_response.get("model_used", assistant_config.get("model_id", "AI助手")),
                "confidence": ai_response.get("confidence", 0.8),
                "response_time": ai_response.get("response_time", 0)
            }

        except ImportError:
            # 如果AI助手服务不可用，使用模拟响应
            logger.warning("AI助手服务不可用，使用模拟响应")
            if "你好" in message or "hello" in message.lower():
                response = "您好！我是AI智能客服，很高兴为您服务。有什么可以帮助您的吗？"
            elif "价格" in message or "多少钱" in message:
                response = "关于价格信息，我建议您查看我们的官方价格页面，或者联系我们的销售团队获取最新的报价。"
            elif "技术" in message or "问题" in message:
                response = "我理解您遇到了技术问题。请详细描述您的问题，我会尽力帮助您解决。"
            elif image_context:
                response = f"我看到您上传了图片。{image_context} 请问您需要我帮您分析什么？"
            else:
                response = f"感谢您的咨询。关于「{message}」，我正在为您查找相关信息。如果您需要更详细的帮助，我可以为您转接人工客服。"

            return {
                "response": response,
                "model_used": assistant_config.get("model_id", "AI助手"),
                "confidence": 0.85
            }
        
    except Exception as e:
        logger.error(f"AI助手调用失败: {e}")
        return {
            "response": "抱歉，我暂时无法处理您的请求，请稍后再试。",
            "model_used": "fallback",
            "confidence": 0.0
        }
