#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI客服系统整合 API
"""

import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from api.deps import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User
from models.project import Project
from ..services.customer_service import get_customer_service_manager

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 请求和响应模型
class IntegrationSettings(BaseModel):
    ai_model_id: Optional[str] = None
    knowledge_base_ids: List[str] = []
    temperature: float = 0.7
    max_tokens: int = 2000
    confidence_threshold: float = 0.8
    enable_context_memory: bool = True
    enable_knowledge_search: bool = True
    fallback_to_human: bool = True

class IntegrationSettingsResponse(BaseModel):
    ai_model_id: Optional[str]
    knowledge_base_ids: List[str]
    temperature: float
    max_tokens: int
    confidence_threshold: float
    enable_context_memory: bool
    enable_knowledge_search: bool
    fallback_to_human: bool
    created_at: datetime
    updated_at: datetime

@router.get(
    "/settings",
    response_model=IntegrationSettingsResponse,
    summary="获取系统整合设置",
    description="获取AI客服的系统整合配置"
)
async def get_integration_settings(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取系统整合设置"""
    try:
        # 获取客服管理器实例
        cs_manager = get_customer_service_manager()
        
        # 获取整合设置
        settings = await cs_manager.get_integration_settings(
            project_id=project.id,
            db=db
        )
        
        return IntegrationSettingsResponse(
            ai_model_id=settings.get("ai_model_id"),
            knowledge_base_ids=settings.get("knowledge_base_ids", []),
            temperature=settings.get("temperature", 0.7),
            max_tokens=settings.get("max_tokens", 2000),
            confidence_threshold=settings.get("confidence_threshold", 0.8),
            enable_context_memory=settings.get("enable_context_memory", True),
            enable_knowledge_search=settings.get("enable_knowledge_search", True),
            fallback_to_human=settings.get("fallback_to_human", True),
            created_at=settings.get("created_at", datetime.utcnow()),
            updated_at=settings.get("updated_at", datetime.utcnow())
        )
        
    except Exception as e:
        logger.error(f"获取系统整合设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统整合设置失败: {str(e)}"
        )

@router.put(
    "/settings",
    response_model=IntegrationSettingsResponse,
    summary="更新系统整合设置",
    description="更新AI客服的系统整合配置"
)
async def update_integration_settings(
    settings_data: IntegrationSettings,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """更新系统整合设置"""
    try:
        # 获取客服管理器实例
        cs_manager = get_customer_service_manager()
        
        # 更新整合设置
        updated_settings = await cs_manager.update_integration_settings(
            project_id=project.id,
            db=db,
            settings_data=settings_data.dict()
        )
        
        return IntegrationSettingsResponse(
            ai_model_id=updated_settings.get("ai_model_id"),
            knowledge_base_ids=updated_settings.get("knowledge_base_ids", []),
            temperature=updated_settings.get("temperature", 0.7),
            max_tokens=updated_settings.get("max_tokens", 2000),
            confidence_threshold=updated_settings.get("confidence_threshold", 0.8),
            enable_context_memory=updated_settings.get("enable_context_memory", True),
            enable_knowledge_search=updated_settings.get("enable_knowledge_search", True),
            fallback_to_human=updated_settings.get("fallback_to_human", True),
            created_at=updated_settings.get("created_at", datetime.utcnow()),
            updated_at=updated_settings.get("updated_at", datetime.utcnow())
        )
        
    except Exception as e:
        logger.error(f"更新系统整合设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新系统整合设置失败: {str(e)}"
        )

@router.get(
    "/test",
    summary="测试系统整合",
    description="测试AI模型和知识库的连接状态"
)
async def test_integration(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """测试系统整合"""
    try:
        # 获取客服管理器实例
        cs_manager = get_customer_service_manager()
        
        # 测试整合状态
        test_results = await cs_manager.test_integration(
            project_id=project.id,
            db=db
        )
        
        return {
            "success": True,
            "message": "系统整合测试完成",
            "results": test_results
        }
        
    except Exception as e:
        logger.error(f"测试系统整合失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"测试系统整合失败: {str(e)}"
        )
