#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 智能体客服 API 接口
"""

from fastapi import APIRouter

# 导入各个API模块
from .sessions import router as sessions_router
from .messages import router as messages_router
from .analytics import router as analytics_router
from .platforms import router as platforms_router
from .tickets import router as tickets_router
from .marketing import router as marketing_router
from .learning import router as learning_router

# 导入公共API和Webhook
from ..public.widget import router as public_widget_router
from ..webhooks.wechat_mp import router as wechat_mp_webhook_router

# 创建主路由器
router = APIRouter()

# 注册子路由
router.include_router(sessions_router, prefix="/sessions", tags=["客服会话"])
router.include_router(messages_router, prefix="/messages", tags=["客服消息"])
router.include_router(analytics_router, prefix="/analytics", tags=["客服分析"])
router.include_router(platforms_router, prefix="/platforms", tags=["平台管理"])
router.include_router(tickets_router, prefix="/tickets", tags=["工单管理"])
router.include_router(marketing_router, prefix="/marketing", tags=["智能营销"])
router.include_router(learning_router, prefix="/learning", tags=["智能学习"])

# 创建公共路由器（无需认证）
public_router = APIRouter()
public_router.include_router(public_widget_router, prefix="", tags=["公共客服组件"])

# 创建Webhook路由器
webhook_router = APIRouter()
webhook_router.include_router(wechat_mp_webhook_router, prefix="/wechat-mp", tags=["微信公众号Webhook"])

__all__ = ["router", "public_router", "webhook_router"]
