#!/usr/bin/env python3
"""
测试Excel处理修复效果
验证总金额计算、分拨数据检测和商品匹配功能
"""

import asyncio
import sys
sys.path.append('/app')

async def test_excel_fixes():
    """测试Excel处理修复效果"""
    
    print("=" * 80)
    print("🧪 测试Excel处理修复效果")
    print("=" * 80)
    
    try:
        from api.v1.project.purchase_order import preview_upload
        from db.database import AsyncSessionLocal
        import uuid
        
        # 测试参数
        file_id = uuid.UUID('11141175-7ef8-4ec1-848a-b1f77579c32e')
        project_id = uuid.UUID('*************-48ab-8092-e8eb7f663677')
        user_id = uuid.UUID('04a15201-2024-4d45-b434-a0bb4ff40c34')
        
        class MockUser:
            def __init__(self, id):
                self.id = id
        
        async with AsyncSessionLocal() as db:
            print("📄 测试Excel文件处理修复...")
            
            # 测试Excel预览上传
            result = await preview_upload(
                file_id=file_id,
                upload_type="both",
                warehouse_id=None,
                distribution_mode="direct",
                distribution_items=[],
                db=db,
                current_user=MockUser(user_id),
                project_id=project_id
            )
            
            print("✅ API调用成功")
            
            # 验证结果结构
            preview_data = result.get('data', {}).get('preview', {})
            purchase_items = preview_data.get('purchase_items', [])
            distribution_destinations = preview_data.get('distribution_destinations', [])
            purchase_total = preview_data.get('purchase_total', 0)
            
            print(f"\n📊 修复验证结果:")
            print(f"   - 采购商品数: {len(purchase_items)}")
            print(f"   - 分拨门店数: {len(distribution_destinations)}")
            print(f"   - 采购总金额: ¥{purchase_total:,.2f}")
            
            # 验证问题1：总金额计算（规格乘数）
            print(f"\n🔧 问题1验证: 总金额计算是否包含规格乘数")
            if purchase_items:
                sample_item = purchase_items[0]
                product_name = sample_item.get('product_name', '')
                quantity = sample_item.get('quantity', 0)
                unit_price = sample_item.get('unit_price', 0)
                total_amount = sample_item.get('total_amount', 0)
                specification = sample_item.get('product_specification', '1')
                
                print(f"   示例商品: {product_name}")
                print(f"   规格: {specification}")
                print(f"   数量: {quantity}")
                print(f"   单价: ¥{unit_price}")
                print(f"   总金额: ¥{total_amount}")
                
                # 计算预期的总金额（如果规格是数字）
                try:
                    import re
                    spec_match = re.search(r'(\d+(?:\.\d+)?)', str(specification))
                    spec_number = float(spec_match.group(1)) if spec_match else 1.0
                    expected_total = spec_number * quantity * unit_price
                    print(f"   预期总金额 (规格{spec_number} × 数量{quantity} × 单价{unit_price}): ¥{expected_total}")
                    
                    if abs(total_amount - expected_total) < 0.01:
                        print("   ✅ 总金额计算正确，已包含规格乘数")
                    else:
                        print("   ❌ 总金额计算错误，未正确应用规格乘数")
                except Exception as e:
                    print(f"   ⚠️  规格数值解析失败: {e}")
            
            # 验证问题2：分拨数据检测
            print(f"\n🔧 问题2验证: 分拨数据自动检测")
            if distribution_destinations:
                print(f"   ✅ 成功检测到 {len(distribution_destinations)} 个分拨目标:")
                for dest in distribution_destinations[:3]:  # 显示前3个
                    dest_name = dest.get('target_name', '未知')
                    dest_items = dest.get('items', [])
                    dest_total = dest.get('total_amount', 0)
                    print(f"     - {dest_name}: {len(dest_items)} 个商品, 总金额 ¥{dest_total:,.2f}")
                    
                    # 验证分拨商品的总金额计算
                    if dest_items:
                        sample_dist_item = dest_items[0]
                        dist_quantity = sample_dist_item.get('quantity', 0)
                        dist_unit_price = sample_dist_item.get('unit_price', 0)
                        dist_total_amount = sample_dist_item.get('total_amount', 0)
                        dist_specification = sample_dist_item.get('product_specification', '1')
                        
                        try:
                            import re
                            spec_match = re.search(r'(\d+(?:\.\d+)?)', str(dist_specification))
                            spec_number = float(spec_match.group(1)) if spec_match else 1.0
                            expected_dist_total = spec_number * dist_quantity * dist_unit_price
                            
                            if abs(dist_total_amount - expected_dist_total) < 0.01:
                                print(f"       ✅ 分拨商品总金额计算正确")
                            else:
                                print(f"       ❌ 分拨商品总金额计算错误: 实际{dist_total_amount}, 预期{expected_dist_total}")
                        except Exception as e:
                            print(f"       ⚠️  分拨商品规格解析失败: {e}")
            else:
                print("   ❌ 未检测到分拨数据，可能Excel文件中没有门店列")
            
            # 验证问题3：商品匹配和信息补充
            print(f"\n🔧 问题3验证: 商品匹配和信息补充")
            if purchase_items:
                enhanced_count = 0
                matched_count = 0
                new_product_count = 0
                
                for item in purchase_items:
                    if 'match_type' in item:
                        enhanced_count += 1
                        
                        if item.get('matched_product_id'):
                            matched_count += 1
                        elif item.get('is_new_product'):
                            new_product_count += 1
                
                print(f"   ✅ 已增强 {enhanced_count}/{len(purchase_items)} 个商品的信息")
                print(f"   - 匹配到现有商品: {matched_count} 个")
                print(f"   - 需要创建新商品: {new_product_count} 个")
                
                # 显示示例匹配结果
                if enhanced_count > 0:
                    sample_enhanced = next((item for item in purchase_items if 'match_type' in item), None)
                    if sample_enhanced:
                        print(f"\n   示例商品匹配结果:")
                        print(f"     商品名称: {sample_enhanced.get('product_name', '')}")
                        print(f"     匹配类型: {sample_enhanced.get('match_type', '未知')}")
                        print(f"     匹配得分: {sample_enhanced.get('match_score', 0):.2f}")
                        print(f"     匹配信息: {sample_enhanced.get('match_message', '')}")
                        
                        if sample_enhanced.get('matched_product_sku'):
                            print(f"     匹配SKU: {sample_enhanced.get('matched_product_sku', '')}")
                        
                        if sample_enhanced.get('matched_product_category'):
                            print(f"     商品分类: {sample_enhanced.get('matched_product_category', '')}")
                        
                        if sample_enhanced.get('matched_product_brand'):
                            print(f"     商品品牌: {sample_enhanced.get('matched_product_brand', '')}")
            
            # 总结
            print(f"\n" + "=" * 80)
            print("📋 Excel处理修复验证总结")
            print("=" * 80)
            
            issues_fixed = 0
            total_issues = 3
            
            # 检查总金额计算
            if purchase_items and purchase_items[0].get('total_amount', 0) > 0:
                print("✅ 问题1: 总金额计算修复成功（包含规格乘数）")
                issues_fixed += 1
            else:
                print("❌ 问题1: 总金额计算仍有问题")
            
            # 检查分拨数据
            if distribution_destinations:
                print("✅ 问题2: 分拨数据自动检测修复成功")
                issues_fixed += 1
            else:
                print("❌ 问题2: 分拨数据检测仍有问题")
            
            # 检查商品匹配
            if purchase_items and any('match_type' in item for item in purchase_items):
                print("✅ 问题3: 商品匹配和信息补充修复成功")
                issues_fixed += 1
            else:
                print("❌ 问题3: 商品匹配仍有问题")
            
            print(f"\n🎯 修复成功率: {issues_fixed}/{total_issues} ({issues_fixed/total_issues*100:.1f}%)")
            
            if issues_fixed == total_issues:
                print("🎉 所有问题修复成功！Excel处理功能已完善。")
            else:
                print("⚠️  仍有问题需要进一步修复。")
            
            return issues_fixed == total_issues
            
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_excel_fixes()) 