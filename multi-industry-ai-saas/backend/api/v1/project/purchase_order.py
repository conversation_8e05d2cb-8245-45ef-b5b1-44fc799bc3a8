#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import io
import json
import os
import pandas as pd
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, File, UploadFile, Form, Body
from fastapi.responses import Response
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import parse_obj_as
from datetime import datetime, date, timedelta
import uuid
from sqlalchemy import select

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project, get_current_project_id, get_current_tenant_id
from models.project import Project
from models.user import User
from models.store import Store
from models.warehouse import Warehouse
from models.supplier import Supplier
from models.purchase_order import PurchaseOrder
from schemas.purchase_order import (
    PurchaseOrderCreate,
    PurchaseOrderUpdate,
    PurchaseOrderStatusUpdate,
    PurchaseOrderPaymentUpdate,
    PurchaseOrderResponse,
    PurchaseOrderListResponse,
    PurchaseOrderItemCreate,
    PurchaseOrderItemUpdate,
    UploadResponse,
    DistributionItemCreate,
    PurchaseItemCreate,
    DistributionTarget,
    PreviewUploadRequest,
    ConfirmUploadRequest
)
from services.purchase_order import PurchaseOrderService
from services.table_processing_service import TableProcessingService
from schemas.table_processing import TableProcessingRequest
from services.product_service import ProductService

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# --- 静态路由优先 ---
@router.get("/template", response_model=None)
async def get_template(
    type: str = Query("both", description="模板类型: both, purchase, distribution"),
    project_id: uuid.UUID = Depends(get_current_project_id),
    current_user: User = Depends(get_current_user)
):
    """
    获取采购分拨模板
    """
    try:
        # 验证模板类型
        if type not in ["both", "purchase", "distribution"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="模板类型无效，必须是 'both', 'purchase' 或 'distribution'"
            )

        # 创建示例数据
        if type == "both":
            data = {
                "商品名称": ["神仙果苹果", "青甜小番茄", "有机生菜"],
                "商品分类": ["水果", "蔬菜", "蔬菜"],
                "商品品牌": ["神仙果", "青甜", "有机农场"],
                "单位": ["公斤", "公斤", "包"],
                "规格": ["17", "2.8", "200g"],
                "单价": [27, 28, 8],
                "合计": [7, 5, 10],
                "张斌桥": [2, 2, 4],
                "江东": [2, 1, 3],
                "江南二区": [3, 2, 3]
            }
            sheet_name = '采购分拨单模板'
            filename = 'purchase_distribution_template.xlsx'
        elif type == "purchase":
            data = {
                "商品名称": ["示例商品1", "示例商品2", "示例商品3"],
                "商品编码": ["P001", "P002", "P003"],
                "商品分类": ["分类A", "分类B", "分类C"],
                "商品品牌": ["品牌A", "品牌B", "品牌C"],
                "规格": ["规格1", "规格2", "规格3"],
                "单位": ["个", "箱", "kg"],
                "数量": [10, 5, 2.5],
                "单价": [100, 200, 50],
                "备注": ["备注1", "备注2", "备注3"]
            }
            sheet_name = '采购单模板'
            filename = 'purchase_template.xlsx'
        else:  # distribution
            data = {
                "商品名称": ["示例商品1", "示例商品2", "示例商品3"],
                "商品编码": ["P001", "P002", "P003"],
                "商品分类": ["分类A", "分类B", "分类C"],
                "商品品牌": ["品牌A", "品牌B", "品牌C"],
                "规格": ["规格1", "规格2", "规格3"],
                "单位": ["个", "箱", "kg"],
                "数量": [10, 5, 2.5],
                "门店A": [5, 3, 1.5],
                "门店B": [3, 1, 0.5],
                "仓库C": [2, 1, 0.5],
                "备注": ["备注1", "备注2", "备注3"]
            }
            sheet_name = '分拨单模板'
            filename = 'distribution_template.xlsx'

        df = pd.DataFrame(data)

        # 创建内存中的Excel文件
        output = io.BytesIO()
        
        # 使用openpyxl引擎而不是xlsxwriter
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=False)

            # 获取工作表
            workbook = writer.book
            worksheet = writer.sheets[sheet_name]

            # 设置表头样式（使用openpyxl的方式）
            from openpyxl.styles import Font, PatternFill, Border, Side
            
            header_font = Font(bold=True)
            header_fill = PatternFill(start_color="D7E4BC", end_color="D7E4BC", fill_type="solid")
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # 应用表头样式
            for col_num, column in enumerate(df.columns, 1):
                cell = worksheet.cell(row=1, column=col_num)
                cell.font = header_font
                cell.fill = header_fill
                cell.border = border

            # 设置列宽
            column_widths = {
                "商品名称": 20,
                "商品编码": 15,
                "商品分类": 15,
                "商品品牌": 15,
                "规格": 15,
                "单位": 10,
                "数量": 10,
                "合计": 10,
                "单价": 10,
                "备注": 20
            }
            
            for col_num, column in enumerate(df.columns, 1):
                if column in column_widths:
                    worksheet.column_dimensions[worksheet.cell(row=1, column=col_num).column_letter].width = column_widths[column]
                elif column in ["张斌桥", "江东", "江南二区", "门店A", "门店B", "仓库C"]:
                    # 分拨目标列宽度
                    worksheet.column_dimensions[worksheet.cell(row=1, column=col_num).column_letter].width = 12
                elif "分拨目标" in column:
                    worksheet.column_dimensions[worksheet.cell(row=1, column=col_num).column_letter].width = 15
                elif "分拨数量" in column:
                    worksheet.column_dimensions[worksheet.cell(row=1, column=col_num).column_letter].width = 10

            # 添加说明
            row_offset = len(df) + 3
            
            # 说明标题
            title_cell = worksheet.cell(row=row_offset, column=1, value="说明：")
            title_cell.font = Font(bold=True)

            # 说明内容
            if type == "both":
                worksheet.cell(row=row_offset + 1, column=1, value="1. 商品名称、单价和合计为必填项")
                worksheet.cell(row=row_offset + 2, column=1, value="2. 商品分类和商品品牌为选填项，有助于AI更好地归类商品")
                worksheet.cell(row=row_offset + 3, column=1, value="3. 分拨目标列（如门店名称）包含该商品分配给对应目标的数量")
                worksheet.cell(row=row_offset + 4, column=1, value="4. 各分拨目标数量总和应等于或小于合计数量")
                worksheet.cell(row=row_offset + 5, column=1, value="5. 可根据实际情况添加或删除分拨目标列")
                worksheet.cell(row=row_offset + 6, column=1, value="6. 规格、单位为选填项")
            elif type == "purchase":
                worksheet.cell(row=row_offset + 1, column=1, value="1. 商品名称、数量和单价为必填项")
                worksheet.cell(row=row_offset + 2, column=1, value="2. 商品编码、商品分类、商品品牌、规格、单位和备注为选填项")
                worksheet.cell(row=row_offset + 3, column=1, value="3. 商品分类和品牌有助于系统更好地管理和归类商品")
            else:  # distribution
                worksheet.cell(row=row_offset + 1, column=1, value="1. 商品名称和数量为必填项")
                worksheet.cell(row=row_offset + 2, column=1, value="2. 分拨目标列（如门店名称）包含该商品分配给对应目标的数量")
                worksheet.cell(row=row_offset + 3, column=1, value="3. 各分拨目标数量总和应等于或小于总数量")
                worksheet.cell(row=row_offset + 4, column=1, value="4. 可根据实际情况添加或删除分拨目标列")
                worksheet.cell(row=row_offset + 5, column=1, value="5. 商品编码、商品分类、商品品牌、规格、单位和备注为选填项")
                worksheet.cell(row=row_offset + 6, column=1, value="6. 商品分类和品牌有助于系统更好地管理和归类商品")

        # 设置响应头
        output.seek(0)

        return Response(
            content=output.getvalue(),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模板失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取模板失败: {str(e)}"
        )

@router.get("", response_model=PurchaseOrderListResponse)
async def get_purchase_orders(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    warehouse_id: Optional[uuid.UUID] = None,
    order_status: Optional[str] = Query(None, alias="status"),
    payment_status: Optional[str] = None,
    search: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    sort_by: str = "created_at",
    sort_order: str = "desc",
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取采购订单列表
    """
    try:
        # 转换日期参数
        start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
        end_datetime = datetime.combine(end_date, datetime.max.time()) if end_date else None

        # 获取采购订单列表
        orders = await PurchaseOrderService.get_purchase_orders(
            db=db,
            project_id=project.id,
            skip=skip,
            limit=limit,
            warehouse_id=warehouse_id,
            status=order_status,
            payment_status=payment_status,
            search=search,
            start_date=start_datetime,
            end_date=end_datetime,
            sort_by=sort_by,
            sort_order=sort_order
        )

        # 获取总数
        total = await PurchaseOrderService.count_purchase_orders(
            db=db,
            project_id=project.id,
            warehouse_id=warehouse_id,
            status=order_status,
            payment_status=payment_status,
            search=search,
            start_date=start_datetime,
            end_date=end_datetime
        )

        # 转换为响应格式
        order_list = []
        for order in orders:
            order_detail = await PurchaseOrderService.get_purchase_order_with_details(db, order.id)
            if order_detail:
                order_list.append(order_detail)

        return PurchaseOrderListResponse(
            items=order_list,
            total=total,
            skip=skip,
            limit=limit
        )
    except Exception as e:
        logger.error(f"获取采购订单列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取采购订单列表失败: {str(e)}"
        )

@router.post("", response_model=PurchaseOrderResponse, status_code=status.HTTP_201_CREATED)
async def create_purchase_order(
    order_data: PurchaseOrderCreate,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建采购订单
    """
    try:
        # 创建采购订单
        order = await PurchaseOrderService.create_purchase_order(
            db=db,
            order_data=order_data,
            project_id=project.id,
            user_id=current_user.id
        )

        # 获取采购订单详情
        order_detail = await PurchaseOrderService.get_purchase_order_with_details(db, order.id)
        if not order_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        return parse_obj_as(PurchaseOrderResponse, order_detail)
    except ValueError as e:
        logger.error(f"创建采购订单失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"创建采购订单失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建采购订单失败: {str(e)}"
        )

# --- 动态路由在后 ---
@router.get("/{order_id}", response_model=PurchaseOrderResponse)
async def get_purchase_order(
    order_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取采购订单详情
    """
    try:
        # 获取采购订单
        order = await PurchaseOrderService.get_purchase_order_by_id(db, order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        # 检查采购订单是否属于当前项目
        if order.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此采购订单"
            )

        # 获取采购订单详情
        order_detail = await PurchaseOrderService.get_purchase_order_with_details(db, order_id)
        if not order_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        return parse_obj_as(PurchaseOrderResponse, order_detail)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取采购订单详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取采购订单详情失败: {str(e)}"
        )

@router.put("/{order_id}", response_model=PurchaseOrderResponse)
async def update_purchase_order(
    order_id: uuid.UUID = Path(...),
    order_data: PurchaseOrderUpdate = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新采购订单
    """
    try:
        # 获取采购订单
        order = await PurchaseOrderService.get_purchase_order_by_id(db, order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        # 检查采购订单是否属于当前项目
        if order.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此采购订单"
            )

        # 更新采购订单
        updated_order = await PurchaseOrderService.update_purchase_order(
            db=db,
            order_id=order_id,
            order_data=order_data,
            user_id=current_user.id
        )

        if not updated_order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        # 获取采购订单详情
        order_detail = await PurchaseOrderService.get_purchase_order_with_details(db, order_id)
        if not order_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        return parse_obj_as(PurchaseOrderResponse, order_detail)
    except ValueError as e:
        logger.error(f"更新采购订单失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新采购订单失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新采购订单失败: {str(e)}"
        )

@router.put("/{order_id}/status", response_model=PurchaseOrderResponse)
async def update_purchase_order_status(
    order_id: uuid.UUID = Path(...),
    status_data: PurchaseOrderStatusUpdate = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新采购订单状态
    """
    try:
        # 获取采购订单
        order = await PurchaseOrderService.get_purchase_order_by_id(db, order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        # 检查采购订单是否属于当前项目
        if order.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此采购订单状态"
            )

        # 更新采购订单状态
        updated_order = await PurchaseOrderService.update_purchase_order_status(
            db=db,
            order_id=order_id,
            status_data=status_data,
            user_id=current_user.id
        )

        if not updated_order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        # 获取采购订单详情
        order_detail = await PurchaseOrderService.get_purchase_order_with_details(db, order_id)
        if not order_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        return parse_obj_as(PurchaseOrderResponse, order_detail)
    except ValueError as e:
        logger.error(f"更新采购订单状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新采购订单状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新采购订单状态失败: {str(e)}"
        )

@router.put("/{order_id}/payment", response_model=PurchaseOrderResponse)
async def update_purchase_order_payment(
    order_id: uuid.UUID = Path(...),
    payment_data: PurchaseOrderPaymentUpdate = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新采购订单支付状态
    """
    try:
        # 获取采购订单
        order = await PurchaseOrderService.get_purchase_order_by_id(db, order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        # 检查采购订单是否属于当前项目
        if order.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此采购订单支付状态"
            )

        # 更新采购订单支付状态
        updated_order = await PurchaseOrderService.update_purchase_order_payment(
            db=db,
            order_id=order_id,
            payment_data=payment_data,
            user_id=current_user.id
        )

        if not updated_order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        # 获取采购订单详情
        order_detail = await PurchaseOrderService.get_purchase_order_with_details(db, order_id)
        if not order_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        return parse_obj_as(PurchaseOrderResponse, order_detail)
    except ValueError as e:
        logger.error(f"更新采购订单支付状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新采购订单支付状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新采购订单支付状态失败: {str(e)}"
        )

@router.delete("/{order_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_purchase_order(
    order_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除采购订单
    """
    try:
        # 获取采购订单
        order = await PurchaseOrderService.get_purchase_order_by_id(db, order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        # 检查采购订单是否属于当前项目
        if order.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除此采购订单"
            )

        # 删除采购订单
        success = await PurchaseOrderService.delete_purchase_order(db, order_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除采购订单失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除采购订单失败: {str(e)}"
        )

@router.post("/{order_id}/items", response_model=PurchaseOrderResponse)
async def add_purchase_order_item(
    order_id: uuid.UUID = Path(...),
    item_data: PurchaseOrderItemCreate = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    添加采购订单项
    """
    try:
        # 获取采购订单
        order = await PurchaseOrderService.get_purchase_order_by_id(db, order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        # 检查采购订单是否属于当前项目
        if order.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此采购订单"
            )

        # 添加采购订单项
        updated_order, _ = await PurchaseOrderService.add_purchase_order_item(
            db=db,
            order_id=order_id,
            item_data=item_data.dict(),
            user_id=current_user.id
        )

        if not updated_order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        # 获取采购订单详情
        order_detail = await PurchaseOrderService.get_purchase_order_with_details(db, order_id)
        if not order_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        return parse_obj_as(PurchaseOrderResponse, order_detail)
    except ValueError as e:
        logger.error(f"添加采购订单项失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加采购订单项失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加采购订单项失败: {str(e)}"
        )

@router.put("/{order_id}/items/{item_id}", response_model=PurchaseOrderResponse)
async def update_purchase_order_item(
    order_id: uuid.UUID = Path(...),
    item_id: uuid.UUID = Path(...),
    item_data: PurchaseOrderItemUpdate = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新采购订单项
    """
    try:
        # 获取采购订单
        order = await PurchaseOrderService.get_purchase_order_by_id(db, order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        # 检查采购订单是否属于当前项目
        if order.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此采购订单"
            )

        # 更新采购订单项
        updated_order, _ = await PurchaseOrderService.update_purchase_order_item(
            db=db,
            item_id=item_id,
            item_data=item_data.dict(exclude_unset=True),
            user_id=current_user.id
        )

        if not updated_order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单项不存在"
            )

        # 获取采购订单详情
        order_detail = await PurchaseOrderService.get_purchase_order_with_details(db, order_id)
        if not order_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        return parse_obj_as(PurchaseOrderResponse, order_detail)
    except ValueError as e:
        logger.error(f"更新采购订单项失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新采购订单项失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新采购订单项失败: {str(e)}"
        )

@router.delete("/{order_id}/items/{item_id}", response_model=PurchaseOrderResponse)
async def delete_purchase_order_item(
    order_id: uuid.UUID = Path(...),
    item_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除采购订单项
    """
    try:
        # 获取采购订单
        order = await PurchaseOrderService.get_purchase_order_by_id(db, order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        # 检查采购订单是否属于当前项目
        if order.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此采购订单"
            )

        # 删除采购订单项
        updated_order = await PurchaseOrderService.delete_purchase_order_item(
            db=db,
            item_id=item_id,
            user_id=current_user.id
        )

        if not updated_order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单项不存在"
            )

        # 获取采购订单详情
        order_detail = await PurchaseOrderService.get_purchase_order_with_details(db, order_id)
        if not order_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        return parse_obj_as(PurchaseOrderResponse, order_detail)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除采购订单项失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除采购订单项失败: {str(e)}"
        )

@router.post("/preview-upload", response_model=UploadResponse)
async def preview_upload(
    file_id: uuid.UUID,
    upload_type: str,
    warehouse_id: Optional[uuid.UUID] = None,
    distribution_mode: Optional[str] = None,
    distribution_items: Optional[str] = None,
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    预览上传数据
    """
    try:
        # 验证上传类型
        if upload_type not in ["both", "purchase", "distribution"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="上传类型无效，必须是 'both', 'purchase' 或 'distribution'"
            )

        # 验证分拨模式
        if upload_type != "distribution" and distribution_mode and distribution_mode not in ["direct", "partial"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="分拨模式无效，必须是 'direct' 或 'partial'"
            )

        # 验证分拨商品中的供应商
        if upload_type != "distribution" and distribution_items:
            try:
                items_data = json.loads(distribution_items)
                for item in items_data:
                    supplier_id = item.get("supplier_id")
                    if supplier_id:
                        supplier = await db.get(Supplier, supplier_id)
                        if not supplier or supplier.project_id != project_id:
                            raise HTTPException(
                                status_code=status.HTTP_404_NOT_FOUND,
                                detail=f"商品 '{item.get('product_name')}' 的供应商不存在或不属于当前项目"
                            )
            except json.JSONDecodeError:
                pass

        # 验证仓库
        if upload_type != "distribution" and distribution_mode == "partial" and warehouse_id:
            warehouse = await db.get(Warehouse, warehouse_id)
            if not warehouse or warehouse.project_id != project_id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="仓库不存在或不属于当前项目"
                )

        # 从项目空间获取文件
        from services.storage_service import StorageService
        file_info = await StorageService.get_file_by_id(db, file_id, project_id)

        if not file_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在或不属于当前项目"
            )

        # 检查文件类型，图片文件不支持模板处理，需要AI处理
        file_name = file_info.get("name", "")
        file_extension = os.path.splitext(file_name)[1].lower()
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        
        if file_extension in image_extensions:
            # 为图片文件提供友好的处理建议
            error_detail = {
                "code": "IMAGE_FILE_DETECTED",
                "message": "检测到图片文件，建议使用AI智能识别模式",
                "file_type": "image",
                "file_extension": file_extension,
                "recommended_action": "use_ai_processing",
                "ai_processing_url": f"/api/v1/project/{project_id}/purchase-orders/preview-upload-ai",
                "suggestion": "图片文件包含表格内容时，AI能够更准确地识别和提取数据。请使用AI智能处理模式获得最佳效果。"
            }
            
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=error_detail
            )

        # 获取文件路径
        file_path = file_info.get("storage_path")
        if not file_path or not os.path.exists(file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在或已被删除"
            )

        # 解析分拨商品和目的地
        distribution_destinations = []
        if upload_type != "purchase" and distribution_items:
            try:
                items_data = json.loads(distribution_items)
                for item_data in items_data:
                    product_name = item_data.get("product_name")
                    product_specification = item_data.get("product_specification")
                    product_unit = item_data.get("product_unit")
                    quantity = float(item_data.get("quantity", 0))
                    destinations = item_data.get("destinations", [])

                    if not product_name or quantity <= 0:
                        continue

                    for dest in destinations:
                        dest_type = dest.get("type")
                        dest_id = dest.get("target_id")
                        dest_quantity = float(dest.get("quantity", 0))

                        if not dest_id or dest_quantity <= 0:
                            continue

                        if dest_type not in ["store", "warehouse"]:
                            raise HTTPException(
                                status_code=status.HTTP_400_BAD_REQUEST,
                                detail="分拨目的地类型无效，必须是 'store' 或 'warehouse'"
                            )

                        # 验证目的地
                        if dest_type == "store":
                            target = await db.get(Store, dest_id)
                            if not target or target.project_id != project_id:
                                raise HTTPException(
                                    status_code=status.HTTP_404_NOT_FOUND,
                                    detail=f"门店 {dest_id} 不存在或不属于当前项目"
                                )
                            target_name = target.name
                        else:  # warehouse
                            target = await db.get(Warehouse, dest_id)
                            if not target or target.project_id != project_id:
                                raise HTTPException(
                                    status_code=status.HTTP_404_NOT_FOUND,
                                    detail=f"仓库 {dest_id} 不存在或不属于当前项目"
                                )
                            if distribution_mode == "partial" and dest_id == warehouse_id:
                                raise HTTPException(
                                    status_code=status.HTTP_400_BAD_REQUEST,
                                    detail="入库仓库和分拨目标仓库不能相同"
                                )
                            target_name = target.name

                        # 查找目的地是否已存在
                        dest_found = False
                        for d in distribution_destinations:
                            if d["type"] == dest_type and d["target_id"] == dest_id:
                                # 添加商品到已存在的目的地
                                d["items"].append({
                                    "product_name": product_name,
                                    "product_specification": product_specification,
                                    "product_unit": product_unit,
                                    "quantity": dest_quantity,
                                    "unit_price": item_data.get("unit_price", 0),
                                    "total_amount": dest_quantity * item_data.get("unit_price", 0)
                                })
                                d["total_amount"] += dest_quantity * item_data.get("unit_price", 0)
                                dest_found = True
                                break

                        if not dest_found:
                            # 创建新的目的地
                            distribution_destinations.append({
                                "type": dest_type,
                                "target_id": dest_id,
                                "target_name": target_name,
                                "items": [{
                                    "product_name": product_name,
                                    "product_specification": product_specification,
                                    "product_unit": product_unit,
                                    "quantity": dest_quantity,
                                    "unit_price": item_data.get("unit_price", 0),
                                    "total_amount": dest_quantity * item_data.get("unit_price", 0)
                                }],
                                "total_amount": dest_quantity * item_data.get("unit_price", 0)
                            })
            except json.JSONDecodeError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="分拨商品数据格式无效"
                )

        # 读取Excel文件 - 智能检测表头位置
        try:
            file_extension = os.path.splitext(file_info.get("name", ""))[1].lower()

            if file_extension in ['.xlsx', '.xls']:
                # 智能检测表头行
                df = None
                for header_row in range(5):  # 检查前5行
                    try:
                        temp_df = pd.read_excel(file_path, header=header_row)
                        # 检查是否包含中文列名（通常是表头的标志）
                        chinese_columns = [col for col in temp_df.columns if any('\u4e00' <= char <= '\u9fff' for char in str(col))]
                        if chinese_columns and len(chinese_columns) >= 2:  # 至少有2个中文列名
                            df = temp_df
                            logger.info(f"检测到表头在第{header_row}行，列名: {list(df.columns)[:5]}...")
                            break
                    except Exception:
                        continue
                
                # 如果没有找到合适的表头，使用默认方式读取
                if df is None:
                    df = pd.read_excel(file_path)
                    logger.warning("未检测到明确的表头，使用默认读取方式")
                    
            elif file_extension == '.csv':
                df = pd.read_csv(file_path)
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="不支持的文件格式，请上传.xlsx、.xls或.csv文件"
                )
        except Exception as e:
            logger.error(f"解析文件失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"解析文件失败: {str(e)}"
            )

        # 检查必要的列 - 支持多种列名变体
        def find_column_match(possible_names, df_columns):
            """查找列名匹配，支持多种变体"""
            for possible_name in possible_names:
                # 精确匹配
                if possible_name in df_columns:
                    return possible_name
                # 包含匹配
                for col in df_columns:
                    if possible_name in col or col in possible_name:
                        return col
            return None

        # 定义必要列的可能名称
        column_mappings = {
            'product_name': ['商品名称', '产品名称', '商品', '产品', '货品名称', '品名'],
            'quantity': ['数量', '采购数量', '总数量', '合计', '总计', '数量合计', '采购合计'],
            'unit_price': ['单价', '价格', '采购单价', '进价', '成本价']
        }

        # 查找实际的列名
        actual_columns = {}
        missing_columns = []

        # 检查商品名称列
        product_name_col = find_column_match(column_mappings['product_name'], df.columns)
        if product_name_col:
            actual_columns['product_name'] = product_name_col
        else:
            missing_columns.append('商品名称（或产品名称）')

        # 检查数量列
        quantity_col = find_column_match(column_mappings['quantity'], df.columns)
        if quantity_col:
            actual_columns['quantity'] = quantity_col
        else:
            missing_columns.append('数量（或采购数量）')

        # 检查单价列（非分拨单需要）
        if upload_type != "distribution":
            unit_price_col = find_column_match(column_mappings['unit_price'], df.columns)
            if unit_price_col:
                actual_columns['unit_price'] = unit_price_col
            else:
                missing_columns.append('单价（或价格）')

        if missing_columns:
            # 提供友好的错误信息，包含检测到的列名
            detected_columns = ', '.join(df.columns.tolist())
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"缺少必要的列: {', '.join(missing_columns)}。检测到的列: {detected_columns}。请确保Excel表格包含商品名称、数量等必要字段。"
            )

        # 预览数据
        preview_data = {}

        # 处理采购数据
        if upload_type != "distribution":
            purchase_items = []
            purchase_total = 0.0

            # 查找可选列
            optional_columns = {
                'product_code': find_column_match(['商品编码', '产品编码', '编码', '货号', 'SKU'], df.columns),
                'product_unit': find_column_match(['单位', '计量单位', '规格单位'], df.columns),
                'product_specification': find_column_match(['规格', '产品规格', '型号', '规格型号'], df.columns),
                'notes': find_column_match(['备注', '说明', '描述', '注释'], df.columns)
            }

            for idx, row in df.iterrows():
                # 跳过空行 - 使用实际的列名
                product_name_value = row[actual_columns['product_name']]
                quantity_value = row[actual_columns['quantity']]
                
                if pd.isna(product_name_value) or pd.isna(quantity_value):
                    continue

                quantity = float(quantity_value)
                unit_price = float(row[actual_columns['unit_price']]) if actual_columns.get('unit_price') and not pd.isna(row[actual_columns['unit_price']]) else 0.0
                total_amount = quantity * unit_price

                item = {
                    "index": idx,
                    "product_name": str(product_name_value),
                    "product_code": str(row[optional_columns['product_code']]) if optional_columns['product_code'] and not pd.isna(row[optional_columns['product_code']]) else "",
                    "product_unit": str(row[optional_columns['product_unit']]) if optional_columns['product_unit'] and not pd.isna(row[optional_columns['product_unit']]) else "",
                    "product_specification": str(row[optional_columns['product_specification']]) if optional_columns['product_specification'] and not pd.isna(row[optional_columns['product_specification']]) else "",
                    "quantity": quantity,
                    "unit_price": unit_price,
                    "total_amount": total_amount,
                    "notes": str(row[optional_columns['notes']]) if optional_columns['notes'] and not pd.isna(row[optional_columns['notes']]) else ""
                }

                purchase_items.append(item)
                purchase_total += total_amount

            preview_data["purchase_items"] = purchase_items
            preview_data["purchase_total"] = purchase_total

        # 处理分拨数据
        if upload_type != "purchase" and distribution_destinations:
            preview_data["distribution_destinations"] = distribution_destinations

        # 处理入库数据
        if upload_type != "distribution" and distribution_mode == "partial" and warehouse_id:
            # 获取仓库信息
            warehouse = await db.get(Warehouse, warehouse_id)
            if not warehouse:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="仓库不存在"
                )

            # 计算入库数量
            warehouse_items = []
            warehouse_total = 0.0

            # 从Excel文件中读取商品，计算未分拨的部分
            if upload_type != "purchase" and distribution_destinations:
                # 创建一个字典，记录每个商品已分拨的数量
                distributed_quantities = {}

                for dest in distribution_destinations:
                    for item in dest["items"]:
                        product_name = item["product_name"]
                        if product_name not in distributed_quantities:
                            distributed_quantities[product_name] = 0
                        distributed_quantities[product_name] += item["quantity"]

                # 计算剩余数量
                for idx, row in df.iterrows():
                    # 跳过空行 - 使用实际的列名
                    product_name_value = row[actual_columns['product_name']]
                    quantity_value = row[actual_columns['quantity']]
                    
                    if pd.isna(product_name_value) or pd.isna(quantity_value):
                        continue

                    product_name = str(product_name_value)
                    total_quantity = float(quantity_value)
                    distributed = distributed_quantities.get(product_name, 0)

                    # 计算剩余数量
                    remaining = max(0, total_quantity - distributed)

                    if remaining > 0:
                        unit_price = float(row[actual_columns['unit_price']]) if actual_columns.get('unit_price') and not pd.isna(row[actual_columns['unit_price']]) else 0.0
                        total_amount = remaining * unit_price

                        item = {
                            "index": idx,
                            "product_name": product_name,
                            "product_code": str(row[optional_columns['product_code']]) if optional_columns['product_code'] and not pd.isna(row[optional_columns['product_code']]) else "",
                            "product_unit": str(row[optional_columns['product_unit']]) if optional_columns['product_unit'] and not pd.isna(row[optional_columns['product_unit']]) else "",
                            "product_specification": str(row[optional_columns['product_specification']]) if optional_columns['product_specification'] and not pd.isna(row[optional_columns['product_specification']]) else "",
                            "quantity": remaining,
                            "unit_price": unit_price,
                            "total_amount": total_amount,
                            "notes": str(row[optional_columns['notes']]) if optional_columns['notes'] and not pd.isna(row[optional_columns['notes']]) else ""
                        }

                        warehouse_items.append(item)
                        warehouse_total += total_amount
            else:
                # 如果没有分拨，则全部入库
                for idx, row in df.iterrows():
                    # 跳过空行 - 使用实际的列名
                    product_name_value = row[actual_columns['product_name']]
                    quantity_value = row[actual_columns['quantity']]
                    
                    if pd.isna(product_name_value) or pd.isna(quantity_value):
                        continue

                    quantity = float(quantity_value)
                    unit_price = float(row[actual_columns['unit_price']]) if actual_columns.get('unit_price') and not pd.isna(row[actual_columns['unit_price']]) else 0.0
                    total_amount = quantity * unit_price

                    item = {
                        "index": idx,
                        "product_name": str(product_name_value),
                        "product_code": str(row[optional_columns['product_code']]) if optional_columns['product_code'] and not pd.isna(row[optional_columns['product_code']]) else "",
                        "product_unit": str(row[optional_columns['product_unit']]) if optional_columns['product_unit'] and not pd.isna(row[optional_columns['product_unit']]) else "",
                        "product_specification": str(row[optional_columns['product_specification']]) if optional_columns['product_specification'] and not pd.isna(row[optional_columns['product_specification']]) else "",
                        "quantity": quantity,
                        "unit_price": unit_price,
                        "total_amount": total_amount,
                        "notes": str(row[optional_columns['notes']]) if optional_columns['notes'] and not pd.isna(row[optional_columns['notes']]) else ""
                    }

                    warehouse_items.append(item)
                    warehouse_total += total_amount

            if warehouse_items:
                preview_data["warehouse_entries"] = [{
                    "warehouse_id": warehouse_id,
                    "warehouse_name": warehouse.name,
                    "items": warehouse_items,
                    "total_amount": warehouse_total
                }]

        return {
            "success": True,
            "message": "数据预览成功",
            "data": {
                "preview": preview_data,
                "processing_info": {
                    "method": "template",
                    "template_matched": True,
                    "total_rows": len(df),
                    "valid_rows": len(preview_data.get("purchase_items", [])),
                    "error_rows": 0,
                    "confidence": 1.0,
                    "processing_mode": "template"
                },
                "validation_results": {
                    "errors": [],
                    "warnings": []
                },
                "file_id": str(file_id),
                # 直接将预览数据展平到根级别，便于前端访问
                **preview_data
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"预览上传数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"预览上传数据失败: {str(e)}"
        )

@router.post("/preview-upload-ai", response_model=UploadResponse)
async def preview_upload_with_ai(
    file_id: uuid.UUID,
    upload_type: str,
    warehouse_id: Optional[uuid.UUID] = None,
    distribution_mode: Optional[str] = None,
    distribution_items: Optional[str] = None,
    use_ai: bool = True,
    processing_mode: str = "auto",  # auto, template_only, ai_only
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    使用智能处理采购分拨单预览上传（异步版本）
    
    处理模式说明：
    - auto: 自动选择（推荐）- 优先模板匹配，若模板匹配失败或置信度低则使用AI
    - template_only: 仅模板匹配 - 适用于标准格式表格  
    - ai_only: 仅AI识别 - 适用于复杂或非标准格式
    
    返回：立即返回任务ID，客户端通过WebSocket或轮询获取任务进度和结果
    """
    try:
        logger.info(f"开始创建采购分拨单AI预览任务: file_id={file_id}, processing_mode={processing_mode}")
        
        # 验证上传类型
        if upload_type not in ["both", "purchase", "distribution"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="上传类型无效，必须是 'both', 'purchase' 或 'distribution'"
            )

        # 验证处理模式
        valid_processing_modes = ["auto", "template_only", "ai_only"]
        if processing_mode not in valid_processing_modes:
            logger.warning(f"无效的处理模式: {processing_mode}, 使用默认值 auto")
            processing_mode = "auto"

        # 验证文件是否存在
        from services.storage_service import StorageService
        file_info = await StorageService.get_file_by_id(db, file_id, project_id)

        if not file_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在或不属于当前项目"
            )

        # 创建异步任务
        from services.async_task_service import AsyncTaskService
        
        task_input = {
            "file_id": str(file_id),
            "upload_type": upload_type,
            "warehouse_id": str(warehouse_id) if warehouse_id else None,
            "distribution_mode": distribution_mode,
            "distribution_items": distribution_items,
            "use_ai": use_ai,
            "processing_mode": processing_mode,
            "project_id": str(project_id),
            "tenant_id": str(tenant_id) if tenant_id else None,
            "user_id": str(current_user.id)
        }
        
        task = await AsyncTaskService.create_task(
            db=db,
            task_type="purchase_order_ai_preview",
            task_name=f"采购分拨单AI预览 - {file_info.get('name', '未知文件')}",
            description=f"使用{processing_mode}模式处理采购分拨单",
            user_id=current_user.id,
            project_id=project_id,
            tenant_id=tenant_id,
            input_data=task_input,
            timeout_seconds=600,  # 240秒超时，适合AI图片识别处理
            max_retries=1  # AI任务一般不需要重试
        )
        
        return UploadResponse(
            success=True,
            message="AI处理任务已创建，请通过WebSocket或轮询接口获取处理进度",
            data={
                "task_id": str(task.id),
                "status": "pending",
                "message": "任务正在排队中，请稍候...",
                "file_id": str(file_id),
                "processing_mode": processing_mode,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                # 提供轮询接口信息
                "polling_url": f"/api/v1/project/{project_id}/tasks/{task.id}",
                "websocket_url": f"/api/v1/ws/notifications?user_id={current_user.id}&project_id={project_id}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建采购分拨单预览任务失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建采购分拨单预览任务失败: {str(e)}"
        )

@router.post("/confirm-preview/{task_id}", response_model=UploadResponse)
async def confirm_preview(
    task_id: str,
    request_data: dict,  # 接收前端的预览数据
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    确认预览数据并创建采购订单和分拨记录
    """
    try:
        logger.info(f"确认预览数据: 任务ID={task_id}")
        
        # 使用传入的预览数据
        actual_preview_data = request_data
        
        if not actual_preview_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="预览数据不能为空"
            )
        
        logger.info(f"使用预览数据，采购商品数量: {len(actual_preview_data.get('purchase_items', []))}，分拨目标数量: {len(actual_preview_data.get('distribution_destinations', []))}")
        
        # 提取并解析日期
        purchase_date = None
        distribution_date = None
        
        # 处理采购日期
        if actual_preview_data.get('purchase_date'):
            purchase_date_str = actual_preview_data['purchase_date']
            try:
                if isinstance(purchase_date_str, str):
                    # 尝试解析不同的日期格式
                    if 'T' in purchase_date_str:
                        # ISO格式日期时间
                        purchase_date = datetime.fromisoformat(purchase_date_str.replace('Z', '+00:00'))
                    else:
                        # 日期格式 YYYY-MM-DD
                        purchase_date = datetime.strptime(purchase_date_str, '%Y-%m-%d')
                elif isinstance(purchase_date_str, datetime):
                    purchase_date = purchase_date_str
                logger.info(f"解析采购日期: {purchase_date_str} -> {purchase_date}")
            except Exception as e:
                logger.warning(f"解析采购日期失败: {purchase_date_str}, 错误: {e}")
                purchase_date = datetime.now()
        
        # 处理分拨日期
        if actual_preview_data.get('distribution_date'):
            distribution_date_str = actual_preview_data['distribution_date']
            try:
                if isinstance(distribution_date_str, str):
                    # 尝试解析不同的日期格式
                    if 'T' in distribution_date_str:
                        # ISO格式日期时间
                        distribution_date = datetime.fromisoformat(distribution_date_str.replace('Z', '+00:00'))
                    else:
                        # 日期格式 YYYY-MM-DD
                        distribution_date = datetime.strptime(distribution_date_str, '%Y-%m-%d')
                elif isinstance(distribution_date_str, datetime):
                    distribution_date = distribution_date_str
                logger.info(f"解析分拨日期: {distribution_date_str} -> {distribution_date}")
            except Exception as e:
                logger.warning(f"解析分拨日期失败: {distribution_date_str}, 错误: {e}")
                distribution_date = datetime.now()
        
        # 如果没有提供日期，使用默认值
        if not purchase_date:
            purchase_date = datetime.now()
            logger.info(f"使用默认采购日期: {purchase_date}")
        
        if not distribution_date:
            distribution_date = datetime.now()
            logger.info(f"使用默认分拨日期: {distribution_date}")
        
        # 创建采购订单和分拨记录
        from services.purchase_order import PurchaseOrderService
        from services.product_service import ProductService
        
        created_records = {
            "purchase_orders": 0,
            "distribution_records": 0,
            "arrival_confirmations": 0,
            "new_products": 0,
            "matched_products": 0,
            "failed_products": 0
        }
        
        # 第一步：创建采购订单（如果有采购数据）
        created_purchase_order_id = None
        
        if actual_preview_data.get("purchase_items"):
            purchase_items = actual_preview_data["purchase_items"]
            
            # 使用智能商品匹配和创建服务
            logger.info(f"开始智能匹配和创建商品，商品数量: {len(purchase_items)}")
            
            # 批量处理商品匹配和创建
            product_results = await ProductService.batch_find_or_create_products(
                db=db,
                product_items=purchase_items,
                project_id=project_id,
                tenant_id=tenant_id,
                user_id=current_user.id,
                auto_create=True
            )
            
            # 统计商品处理结果
            new_products_count = sum(1 for r in product_results if r["is_new"])
            matched_products_count = sum(1 for r in product_results if r["success"] and not r["is_new"])
            failed_products_count = sum(1 for r in product_results if not r["success"])
            
            logger.info(f"商品处理完成: 新创建={new_products_count}, 匹配={matched_products_count}, 失败={failed_products_count}")
            
            # 创建单个采购订单包含所有商品
            try:
                # 生成订单编号
                order_number = await PurchaseOrderService.generate_order_number()
                
                # 计算总金额
                total_amount = sum(float(item.get("total_amount", 0)) for item in purchase_items)
                
                # 创建采购订单（不指定供应商，因为供应商是商品级别的）
                from models.purchase_order import PurchaseOrder, PurchaseOrderItem
                
                db_order = PurchaseOrder(
                    tenant_id=tenant_id,
                    order_number=order_number,
                    project_id=project_id,
                    supplier_id=None,  # 采购单级别不指定供应商
                    warehouse_id=None,
                    order_date=purchase_date,
                    expected_delivery_date=purchase_date + timedelta(days=7),
                    status="confirmed",  # 直接设为已确认状态
                    total_amount=total_amount,
                    discount_amount=0.0,
                    tax_amount=0.0,
                    shipping_fee=0.0,
                    final_amount=total_amount,
                    payment_status="unpaid",
                    notes=f"AI智能识别创建 - 任务ID: {task_id}",
                    created_by=current_user.id,
                    updated_by=current_user.id
                )
                
                db.add(db_order)
                await db.flush()
                
                created_purchase_order_id = db_order.id
                logger.info(f"创建采购订单成功: {order_number}, 订单ID: {created_purchase_order_id}")
                
                # 创建采购订单项（供应商在商品级别）
                for item in purchase_items:
                    # 使用匹配或创建的商品ID
                    product_id = item.get("product_id")
                    if product_id:
                        try:
                            product_id = uuid.UUID(product_id)
                        except (ValueError, TypeError):
                            product_id = None
                    
                    # 商品级别的供应商ID
                    supplier_id = item.get("supplier_id")
                    if supplier_id:
                        try:
                            supplier_id = uuid.UUID(supplier_id)
                        except (ValueError, TypeError):
                            supplier_id = None
                    
                    db_item = PurchaseOrderItem(
                        tenant_id=tenant_id,
                        project_id=project_id,
                        purchase_order_id=db_order.id,
                        product_id=product_id,
                        supplier_id=supplier_id,  # 商品级别的供应商
                        product_name=item.get("matched_product_name") or item.get("product_name", ""),
                        product_code=item.get("matched_product_sku") or item.get("product_code", ""),
                        product_unit=item.get("matched_product_unit") or item.get("product_unit", ""),
                        product_specification=item.get("matched_product_specification") or item.get("product_specification", ""),
                        quantity=float(item.get("quantity", 0)),
                        unit_price=float(item.get("unit_price", 0)),
                        discount_rate=0.0,
                        tax_rate=0.0,
                        total_amount=float(item.get("total_amount", 0)),
                        received_quantity=0.0,
                        notes=item.get("notes", "AI识别创建")
                    )
                    db.add(db_item)
                
                created_records["purchase_orders"] += 1
                logger.info(f"创建采购订单及商品成功: {order_number}")
                
                # 更新创建记录统计
                created_records["new_products"] = new_products_count
                created_records["matched_products"] = matched_products_count
                created_records["failed_products"] = failed_products_count
                
            except Exception as e:
                logger.error(f"创建采购订单失败: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"创建采购订单失败: {str(e)}"
                )

        # 第二步：创建分拨记录（到货确认单）并自动关联到刚创建的采购单
        if actual_preview_data.get("distribution_destinations"):
            distribution_destinations = actual_preview_data["distribution_destinations"]
            logger.info(f"开始处理分拨记录，目标数量: {len(distribution_destinations)}")
            
            for destination in distribution_destinations:
                try:
                    # 生成到货单号
                    arrival_number = f"ARR{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6]}"
                    
                    # 验证目标是否存在
                    target_id = destination.get("target_id")
                    target_type = destination.get("type")
                    
                    if not target_id:
                        logger.warning(f"分拨目标缺少target_id，跳过")
                        continue
                    
                    if target_type == "store":
                        from models.store import Store
                        target = await db.get(Store, uuid.UUID(target_id))
                        if not target or target.project_id != project_id:
                            logger.warning(f"门店 {target_id} 不存在或不属于当前项目，跳过")
                            continue
                    elif target_type == "warehouse":
                        from models.warehouse import Warehouse
                        target = await db.get(Warehouse, uuid.UUID(target_id))
                        if not target or target.project_id != project_id:
                            logger.warning(f"仓库 {target_id} 不存在或不属于当前项目，跳过")
                            continue
                    else:
                        logger.warning(f"未知的目标类型: {target_type}，跳过")
                        continue
                    
                    # 创建到货确认单
                    from models.store_operations import ArrivalConfirmation, ArrivalConfirmationItem
                    
                    # 计算分拨商品总金额和数量
                    destination_items = destination.get("items", [])
                    total_amount = destination.get("total_amount", 0.0)
                    total_items = len(destination_items)
                    
                    arrival = ArrivalConfirmation(
                        tenant_id=tenant_id,
                        project_id=project_id,
                        store_id=uuid.UUID(target_id) if target_type == "store" else None,
                        warehouse_id=uuid.UUID(target_id) if target_type == "warehouse" else None,
                        purchase_order_id=created_purchase_order_id,  # 直接关联刚创建的采购单
                        arrival_number=arrival_number,
                        arrival_type="warehouse",  # 仓库配货类型
                        supplier_id=None,  # 分拨单不直接关联供应商
                        arrival_date=distribution_date,
                        status="pending",  # 待确认状态
                        total_amount=total_amount,
                        total_items=total_items,
                        notes=f"AI智能识别创建分拨单 - 目标: {destination.get('target_name', '未知')} (任务ID: {task_id})",
                        created_by=current_user.id,
                        updated_by=current_user.id
                    )
                    
                    db.add(arrival)
                    await db.flush()
                    
                    # 创建到货确认单项
                    for item in destination_items:
                        arrival_item = ArrivalConfirmationItem(
                            arrival_id=arrival.id,
                            product_id=None,  # AI识别的商品可能不在商品库中
                            product_name=item.get("product_name", ""),
                            product_code=item.get("product_code", ""),
                            unit=item.get("product_unit", ""),
                            specification=item.get("product_specification", ""),
                            expected_quantity=int(float(item.get("quantity", 0))),
                            actual_quantity=0,  # 初始为0，等待确认
                            price=float(item.get("unit_price", 0)),
                            amount=float(item.get("total_amount", 0)),
                            is_confirmed=False,
                            notes=item.get("notes", "AI识别创建")
                        )
                        db.add(arrival_item)
                    
                    created_records["arrival_confirmations"] += 1
                    created_records["distribution_records"] += 1
                    logger.info(f"创建到货确认单成功: {arrival_number}, 目标: {destination.get('target_name')}, 关联采购单: {created_purchase_order_id}")
                    
                except Exception as e:
                    logger.error(f"创建到货确认单失败，目标: {destination.get('target_name')}, 错误: {e}")
                    continue
        
        # 提交事务
        await db.commit()
        
        logger.info(f"确认预览完成，创建记录: {created_records}")
        
        return UploadResponse(
            success=True,
            message=f"确认成功！已创建 {created_records['purchase_orders']} 个采购订单，{created_records['arrival_confirmations']} 个到货确认单",
            data={
                "created_records": created_records,
                "task_id": task_id,
                "purchase_order_id": str(created_purchase_order_id) if created_purchase_order_id else None,
                "purchase_date": purchase_date.isoformat() if purchase_date else None,
                "distribution_date": distribution_date.isoformat() if distribution_date else None
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"确认预览失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"确认预览失败: {str(e)}"
        )

@router.get("/{order_id}/arrivals", response_model=Dict[str, Any])
async def get_purchase_order_arrivals(
    order_id: uuid.UUID,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取采购订单关联的分拨单(到货确认)列表
    """
    try:
        # 验证采购订单是否存在
        order_query = select(PurchaseOrder).where(
            PurchaseOrder.id == order_id,
            PurchaseOrder.project_id == project.id
        )
        order_result = await db.execute(order_query)
        order = order_result.scalar_one_or_none()
        
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        # 查询关联的分拨单
        from models.store_operations import ArrivalConfirmation
        from models.store import Store
        from models.warehouse import Warehouse
        
        arrivals_query = (
            select(
                ArrivalConfirmation,
                Store.name.label("store_name"),
                Warehouse.name.label("warehouse_name")
            )
            .outerjoin(Store, ArrivalConfirmation.store_id == Store.id)
            .outerjoin(Warehouse, ArrivalConfirmation.warehouse_id == Warehouse.id)
            .where(ArrivalConfirmation.purchase_order_id == order_id)
            .order_by(ArrivalConfirmation.arrival_date.desc())
        )
        
        arrivals_result = await db.execute(arrivals_query)
        arrivals_rows = arrivals_result.all()
        
        # 构建返回数据
        arrivals = []
        for row in arrivals_rows:
            arrival = row[0]
            store_name = row[1]
            warehouse_name = row[2]
            
            arrival_dict = {
                "id": str(arrival.id),
                "arrival_number": arrival.arrival_number,
                "arrival_type": arrival.arrival_type,
                "arrival_date": arrival.arrival_date.isoformat() if arrival.arrival_date else None,
                "status": arrival.status,
                "total_amount": float(arrival.total_amount) if arrival.total_amount else 0,
                "total_items": arrival.total_items,
                "store_id": str(arrival.store_id) if arrival.store_id else None,
                "warehouse_id": str(arrival.warehouse_id) if arrival.warehouse_id else None,
                "store_name": store_name,
                "warehouse_name": warehouse_name,
                "notes": arrival.notes,
                "created_at": arrival.created_at.isoformat() if arrival.created_at else None
            }
            arrivals.append(arrival_dict)
        
        return {
            "success": True,
            "message": "获取关联分拨单成功",
            "data": {
                "purchase_order": {
                    "id": str(order.id),
                    "order_number": order.order_number,
                    "status": order.status,
                    "total_amount": float(order.total_amount) if order.total_amount else 0
                },
                "arrivals": arrivals,
                "total": len(arrivals)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取采购订单关联分拨单失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取采购订单关联分拨单失败: {str(e)}"
        )

@router.post("/{order_id}/create-arrival", response_model=Dict[str, Any])
async def create_arrival_for_purchase_order(
    order_id: uuid.UUID,
    arrival_data: Dict[str, Any] = Body(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    为采购订单创建分拨单
    """
    try:
        # 验证采购订单是否存在
        order_query = select(PurchaseOrder).where(
            PurchaseOrder.id == order_id,
            PurchaseOrder.project_id == project.id
        )
        order_result = await db.execute(order_query)
        order = order_result.scalar_one_or_none()
        
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购订单不存在"
            )

        # 创建分拨单
        from models.store_operations import ArrivalConfirmation, ArrivalConfirmationItem
        from datetime import datetime
        
        # 生成到货单号
        today = datetime.now().strftime("%Y%m%d")
        timestamp = int(datetime.now().timestamp())
        arrival_number = f"ARR{today}{timestamp % 10000:04d}"
        
        # 获取分拨目标信息
        destination_type = arrival_data.get("destination_type")  # "store" 或 "warehouse"
        destination_id = arrival_data.get("destination_id")
        
        # 创建到货确认单
        arrival = ArrivalConfirmation(
            tenant_id=order.tenant_id,
            project_id=project.id,
            store_id=uuid.UUID(destination_id) if destination_type == "store" else None,
            warehouse_id=uuid.UUID(destination_id) if destination_type == "warehouse" else None,
            purchase_order_id=order.id,  # 关联采购单
            arrival_number=arrival_number,
            arrival_type="warehouse",  # 仓库配货
            arrival_date=datetime.now(),
            status="pending",
            total_amount=arrival_data.get("total_amount", 0),
            total_items=len(arrival_data.get("items", [])),
            notes=f"基于采购单 {order.order_number} 创建的分拨单",
            created_by=current_user.id,
            updated_by=current_user.id
        )
        
        db.add(arrival)
        await db.flush()
        
        # 创建分拨单明细
        for item_data in arrival_data.get("items", []):
            arrival_item = ArrivalConfirmationItem(
                arrival_id=arrival.id,
                product_id=uuid.UUID(item_data["product_id"]) if item_data.get("product_id") else None,
                product_name=item_data["product_name"],
                product_code=item_data.get("product_code"),
                specification=item_data.get("specification"),
                unit=item_data.get("unit"),
                expected_quantity=item_data["expected_quantity"],
                actual_quantity=0,  # 初始为0，等待确认
                price=item_data["price"],
                amount=item_data["expected_quantity"] * item_data["price"],
                is_confirmed=False,
                notes=item_data.get("notes")
            )
            db.add(arrival_item)
        
        await db.commit()
        
        return {
            "success": True,
            "message": "分拨单创建成功",
            "data": {
                "arrival_id": str(arrival.id),
                "arrival_number": arrival.arrival_number,
                "purchase_order_id": str(order.id),
                "purchase_order_number": order.order_number
            }
        }
        
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"为采购订单创建分拨单失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"为采购订单创建分拨单失败: {str(e)}"
        )


