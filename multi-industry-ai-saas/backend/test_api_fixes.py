#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试API修复
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from main import app

def test_api_endpoints():
    """测试API端点"""
    client = TestClient(app)
    
    # 测试健康检查
    print("1. 测试健康检查...")
    response = client.get("/api/v1/health")
    print(f"健康检查: {response.status_code} - {response.json()}")
    
    # 测试插件列表
    print("\n2. 测试插件列表...")
    response = client.get("/api/v1/plugins")
    print(f"插件列表: {response.status_code}")
    if response.status_code == 200:
        plugins = response.json()
        ai_customer_service = None
        for plugin in plugins:
            if plugin.get('code') == 'ai_customer_service':
                ai_customer_service = plugin
                break
        
        if ai_customer_service:
            print(f"AI客服插件已注册: {ai_customer_service['name']}")
        else:
            print("AI客服插件未找到")
    
    # 测试AI客服插件的公共配置
    print("\n3. 测试AI客服插件公共配置...")
    response = client.get("/api/v1/public/ai-customer-service/config/test_widget")
    print(f"公共配置: {response.status_code}")
    if response.status_code == 200:
        print(f"配置内容: {response.json()}")
    
    print("\n4. 测试新增的API端点...")
    
    # 由于需要认证，我们只测试端点是否存在（会返回401而不是404）
    test_endpoints = [
        "/api/v1/project/test-project/plugin/ai-customer-service/analytics/detailed",
        "/api/v1/project/test-project/plugin/ai-customer-service/business-flows",
        "/api/v1/project/test-project/plugin/ai-customer-service/tasks/settings",
        "/api/v1/project/test-project/plugin/ai-customer-service/notifications/settings"
    ]
    
    for endpoint in test_endpoints:
        response = client.get(endpoint)
        if response.status_code == 404:
            print(f"❌ {endpoint}: 端点不存在 (404)")
        elif response.status_code == 401:
            print(f"✅ {endpoint}: 端点存在，需要认证 (401)")
        elif response.status_code == 422:
            print(f"✅ {endpoint}: 端点存在，参数验证失败 (422)")
        else:
            print(f"✅ {endpoint}: 端点存在 ({response.status_code})")

if __name__ == "__main__":
    test_api_endpoints()
