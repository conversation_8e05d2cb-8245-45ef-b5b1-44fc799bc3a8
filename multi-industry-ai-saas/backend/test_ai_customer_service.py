#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 智能体客服插件测试脚本

测试插件的基本功能是否正常工作
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_plugin_import():
    """测试插件导入"""
    try:
        print("测试插件导入...")
        
        # 测试导入插件主模块
        from plugins.ai_customer_service import plugin_info, initialize_plugin, shutdown_plugin
        print(f"✅ 插件主模块导入成功")
        print(f"   插件名称: {plugin_info['name']}")
        print(f"   插件版本: {plugin_info['version']}")
        print(f"   插件作者: {plugin_info['author']}")
        
        # 测试导入核心组件
        from plugins.ai_customer_service.core.platform_adapter import PlatformAdapterManager
        from plugins.ai_customer_service.core.ai_engine import AICustomerServiceEngine
        from plugins.ai_customer_service.core.knowledge_rag import KnowledgeRAGService
        from plugins.ai_customer_service.core.business_flow import BusinessFlowHandler
        from plugins.ai_customer_service.core.learning_optimizer import LearningOptimizer
        print("✅ 核心组件导入成功")
        
        # 测试导入API模块
        from plugins.ai_customer_service.api import router, public_router, webhook_router
        print("✅ API模块导入成功")
        
        # 测试导入模型
        from plugins.ai_customer_service.models.customer_service import (
            CustomerServiceSession,
            CustomerServiceMessage,
            ConversationLog,
            CustomerServiceAnalytics
        )
        print("✅ 数据模型导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 插件导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_plugin_initialization():
    """测试插件初始化"""
    try:
        print("\n测试插件初始化...")
        
        from plugins.ai_customer_service import initialize_plugin, shutdown_plugin, health_check
        
        # 初始化插件
        result = await initialize_plugin()
        if result:
            print("✅ 插件初始化成功")
        else:
            print("❌ 插件初始化失败")
            return False
        
        # 健康检查
        health = await health_check()
        print(f"✅ 插件健康检查: {health['status']}")
        
        # 关闭插件
        result = await shutdown_plugin()
        if result:
            print("✅ 插件关闭成功")
        else:
            print("❌ 插件关闭失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 插件初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_core_components():
    """测试核心组件"""
    try:
        print("\n测试核心组件...")
        
        # 测试平台适配器管理器
        from plugins.ai_customer_service.core.platform_adapter import PlatformAdapterManager
        platform_manager = PlatformAdapterManager()
        print("✅ 平台适配器管理器创建成功")
        
        # 测试AI引擎
        from plugins.ai_customer_service.core.ai_engine import AICustomerServiceEngine
        ai_engine = AICustomerServiceEngine()
        print("✅ AI引擎创建成功")
        
        # 测试知识库服务
        from plugins.ai_customer_service.core.knowledge_rag import KnowledgeRAGService
        knowledge_service = KnowledgeRAGService()
        print("✅ 知识库服务创建成功")
        
        # 测试业务流转处理器
        from plugins.ai_customer_service.core.business_flow import BusinessFlowHandler
        business_flow = BusinessFlowHandler()
        print("✅ 业务流转处理器创建成功")
        
        # 测试学习优化器
        from plugins.ai_customer_service.core.learning_optimizer import LearningOptimizer
        learning_optimizer = LearningOptimizer()
        print("✅ 学习优化器创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_routes():
    """测试API路由"""
    try:
        print("\n测试API路由...")
        
        from plugins.ai_customer_service.api import router, public_router, webhook_router
        
        # 检查路由数量
        main_routes = len(router.routes)
        public_routes = len(public_router.routes)
        webhook_routes = len(webhook_router.routes)
        
        print(f"✅ 主要API路由: {main_routes} 个")
        print(f"✅ 公共API路由: {public_routes} 个")
        print(f"✅ Webhook路由: {webhook_routes} 个")
        
        # 检查路由标签
        main_tags = set()
        for route in router.routes:
            if hasattr(route, 'tags') and route.tags:
                main_tags.update(route.tags)
        
        print(f"✅ API标签: {list(main_tags)}")
        
        return True
        
    except Exception as e:
        print(f"❌ API路由测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_plugin_registration():
    """测试插件注册"""
    try:
        print("\n测试插件注册...")
        
        from plugins.ai_customer_service.register_plugin import check_plugin_status
        
        # 检查插件状态
        await check_plugin_status()
        print("✅ 插件状态检查完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 插件注册测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试 AI 智能体客服插件")
    print("=" * 50)
    
    tests = [
        ("插件导入", test_plugin_import),
        ("插件初始化", test_plugin_initialization),
        ("核心组件", test_core_components),
        ("API路由", test_api_routes),
        ("插件注册", test_plugin_registration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！插件可以正常使用。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
