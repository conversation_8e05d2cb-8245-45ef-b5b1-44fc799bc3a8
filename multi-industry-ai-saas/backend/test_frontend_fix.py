#!/usr/bin/env python3
"""
测试前端修复效果
模拟前端的数据处理逻辑，验证同步处理结果格式
"""

import asyncio
import sys
sys.path.append('/app')

async def test_frontend_fix():
    """测试前端修复效果"""
    
    print("=" * 80)
    print("🧪 测试前端Excel文件处理修复效果")
    print("=" * 80)
    
    try:
        from api.v1.project.purchase_order import preview_upload
        from db.database import AsyncSessionLocal
        import uuid
        
        # 测试参数
        file_id = uuid.UUID('11141175-7ef8-4ec1-848a-b1f77579c32e')
        project_id = uuid.UUID('*************-48ab-8092-e8eb7f663677')
        user_id = uuid.UUID('04a15201-2024-4d45-b434-a0bb4ff40c34')
        
        class MockUser:
            def __init__(self, id):
                self.id = id
        
        async with AsyncSessionLocal() as db:
            print("📄 模拟前端调用previewUpload...")
            
            # 模拟前端的API调用
            result = await preview_upload(
                file_id=file_id,
                upload_type="both",
                warehouse_id=None,
                distribution_mode="direct",
                distribution_items=[],
                db=db,
                current_user=MockUser(user_id),
                project_id=project_id
            )
            
            print("✅ API调用成功，结果结构:")
            print(f"   - success: {result.get('success')}")
            print(f"   - message: {result.get('message')}")
            print(f"   - data keys: {list(result.get('data', {}).keys())}")
            
            # 模拟前端的onComplete回调逻辑
            print("\n🔄 模拟前端onComplete回调处理...")
            
            # 检查是否成功 - 使用修复后的逻辑
            isSuccess = (result.get('success') == True or 
                        result.get('success') == 'true' or 
                        (result.get('message') and 'completed' in result.get('message', '')) or
                        (result.get('result') and isinstance(result.get('result'), dict)))
            
            print(f"   - isSuccess判断: {isSuccess}")
            
            if not isSuccess:
                print("❌ 前端会显示识别失败")
                return False
            
            # 模拟前端的数据格式检测
            print("\n📊 模拟前端数据格式检测...")
            
            actualResult = result
            processingMethod = 'unknown'
            
            # 检测数据来源：同步(模板匹配)还是异步(AI识别)
            if result.get('data') and result['data'].get('processing_info'):
                # 同步处理结果格式
                processingMethod = result['data']['processing_info'].get('method', 'template')
                print(f"   - 检测到同步处理结果，方法: {processingMethod}")
                
                # 转换为统一的结果格式
                preview = result['data'].get('preview', {})
                actualResult = {
                    'success': True,
                    'method': processingMethod,
                    'preview': preview,
                    'purchase_items': preview.get('purchase_items', []),
                    'distribution_destinations': preview.get('distribution_destinations', []),
                    'processing_info': result['data'].get('processing_info'),
                    'validation_results': result['data'].get('validation_results', {}),
                    'file_id': result['data'].get('file_id')
                }
            elif result.get('result') and isinstance(result.get('result'), dict):
                # 异步处理结果格式
                processingMethod = 'ai'
                print(f"   - 检测到异步处理结果")
            else:
                # 直接的结果格式
                processingMethod = result.get('method', 'unknown')
                print(f"   - 直接结果格式，方法: {processingMethod}")
                actualResult = result
            
            # 数据统计信息
            purchaseItems = actualResult.get('purchase_items', [])
            distributionDestinations = actualResult.get('distribution_destinations', [])
            
            itemCount = len(purchaseItems)
            storeCount = len(distributionDestinations)
            
            print(f"   - 处理方法: {processingMethod}")
            print(f"   - 识别商品数: {itemCount}")
            print(f"   - 识别门店数: {storeCount}")
            
            # 根据处理方法显示不同的成功消息
            methodDisplayName = ('模板匹配' if processingMethod == 'template' else 
                               'AI智能识别' if processingMethod == 'ai' else 
                               '混合模式' if processingMethod == 'hybrid' else '智能处理')
            
            print(f"\n🎉 前端应该显示: {methodDisplayName}成功！")
            print(f"   - 描述: 已识别 {itemCount} 个商品{f'，分拨到 {storeCount} 个门店' if storeCount > 1 else ''}")
            
            if itemCount > 0:
                print("\n📦 前3个商品示例:")
                for i, item in enumerate(purchaseItems[:3]):
                    print(f"   {i+1}. {item.get('product_name')} - 数量:{item.get('quantity')} - 单价:¥{item.get('unit_price')}")
            
            print("\n✅ 前端修复验证成功 - 应该不再显示'AI识别失败'")
            return True
            
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_frontend_fix()) 